{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Twitter, Linkedin, Mail, Briefcase } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-50 border-t\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n                <Briefcase className=\"h-5 w-5\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">RemoteWork</span>\n            </div>\n            <p className=\"text-gray-600 mb-4 max-w-md\">\n              Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"https://linkedin.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Linkedin className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Mail className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/blog\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Blog\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n              Legal\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Privacy Policy\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Terms of Service\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-500 text-sm\">\n              © {currentYear} RemoteWork. All rights reserved.\n            </p>\n            <p className=\"text-gray-500 text-sm mt-2 md:mt-0\">\n              Made with ❤️ for remote workers everywhere\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAsD;;;;;;;;;;;sDAIjF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;sDAItF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;sCAQ5F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsD;;;;;;;;;;;sDAIxF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5F,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAwB;oCAChC;oCAAY;;;;;;;0CAEjB,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;uCAEe", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAOA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IACjE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAW,CAAC,OAAO,EAAE,WAAW;0BACnC;;;;;;0BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;IACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;IACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;IACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;AACpD;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg border whitespace-nowrap overflow-hidden text-ellipsis max-w-full';\n\n    const variants = {\n      primary: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-100 text-gray-900 border-gray-300 hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500',\n      outline: 'bg-transparent text-blue-600 border-gray-300 hover:bg-blue-50 hover:border-blue-300 focus:ring-blue-500',\n      ghost: 'bg-transparent text-gray-700 border-transparent hover:bg-gray-100 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm min-h-[32px]',\n      md: 'px-4 py-2 text-sm min-h-[40px]',\n      lg: 'px-6 py-3 text-base min-h-[48px]'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/blog-posts.ts"], "sourcesContent": ["import { Blog<PERSON><PERSON>, Author } from '@/types/blog';\n\nconst author: Author = {\n  name: \"<PERSON>\",\n  bio: \"Remote work consultant and productivity expert with 8+ years of experience helping teams transition to distributed work environments.\",\n  avatar: \"/images/author-avatar.jpg\",\n  social: {\n    twitter: \"https://twitter.com/alex<PERSON><PERSON><PERSON>\",\n    linkedin: \"https://linkedin.com/in/alex<PERSON><PERSON><PERSON>\",\n    website: \"https://alexjohnson.dev\"\n  }\n};\n\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"The Ultimate Guide to Remote Work Productivity\",\n    slug: \"ultimate-guide-remote-work-productivity\",\n    excerpt: \"Discover proven strategies and tools to maximize your productivity while working from home. Learn how to create the perfect remote work environment.\",\n    content: `# The Ultimate Guide to Remote Work Productivity\n\nWorking remotely has become the new normal for millions of professionals worldwide. However, maintaining productivity while working from home presents unique challenges that require specific strategies and tools.\n\n## Creating Your Ideal Workspace\n\nYour physical environment plays a crucial role in your productivity. Here are key elements to consider:\n\n### 1. Dedicated Work Area\n- Choose a quiet space away from distractions\n- Ensure good lighting, preferably natural light\n- Invest in ergonomic furniture\n\n### 2. Essential Tools and Equipment\n- High-quality monitor and keyboard\n- Reliable internet connection\n- Noise-canceling headphones\n- Good webcam for video calls\n\n## Time Management Strategies\n\n### The Pomodoro Technique\nWork in focused 25-minute intervals followed by 5-minute breaks. This technique helps maintain concentration and prevents burnout.\n\n### Time Blocking\nSchedule specific time blocks for different types of work:\n- Deep work sessions\n- Meetings and calls\n- Email and communication\n- Administrative tasks\n\n## Communication Best Practices\n\nEffective communication is vital for remote teams:\n\n- **Over-communicate**: Share updates proactively\n- **Use the right tools**: Choose appropriate channels for different types of communication\n- **Set boundaries**: Establish clear working hours\n- **Regular check-ins**: Schedule consistent team meetings\n\n## Maintaining Work-Life Balance\n\nRemote work can blur the lines between personal and professional life. Here's how to maintain balance:\n\n1. **Set clear boundaries** between work and personal time\n2. **Create rituals** to start and end your workday\n3. **Take regular breaks** and step away from your workspace\n4. **Stay connected** with colleagues and friends\n\n## Conclusion\n\nRemote work productivity isn't just about having the right tools—it's about developing the right habits and mindset. By implementing these strategies consistently, you'll find yourself more productive and satisfied with your remote work experience.`,\n    featuredImage: \"/images/remote-productivity.jpg\",\n    category: \"Productivity\",\n    tags: [\"remote work\", \"productivity\", \"time management\", \"work from home\"],\n    author,\n    publishedAt: \"2024-01-15\",\n    readingTime: 8,\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Building a Strong Remote Team Culture\",\n    slug: \"building-strong-remote-team-culture\",\n    excerpt: \"Learn how to foster connection, collaboration, and culture in distributed teams. Essential strategies for remote team leaders.\",\n    content: `# Building a Strong Remote Team Culture\n\nCreating a cohesive team culture in a remote environment requires intentional effort and the right strategies. Here's how to build and maintain a strong remote team culture.\n\n## Foundation of Remote Culture\n\n### Clear Values and Mission\n- Define your team's core values\n- Communicate the mission regularly\n- Align individual goals with team objectives\n\n### Trust and Transparency\n- Foster open communication\n- Share information freely\n- Trust team members to manage their time\n\n## Virtual Team Building Activities\n\n### Regular Social Interactions\n- Virtual coffee chats\n- Online team games\n- Shared interest groups\n- Virtual lunch meetings\n\n### Collaborative Projects\n- Cross-functional initiatives\n- Mentorship programs\n- Knowledge sharing sessions\n\n## Communication Strategies\n\n### Structured Communication\n- Daily standups\n- Weekly team meetings\n- Monthly all-hands meetings\n- Quarterly reviews\n\n### Informal Communication\n- Slack channels for casual chat\n- Virtual water cooler conversations\n- Peer recognition programs\n\n## Recognition and Celebration\n\nCelebrating wins and recognizing contributions is crucial for remote teams:\n\n- Public recognition in team meetings\n- Peer-to-peer appreciation systems\n- Virtual celebration events\n- Achievement badges or rewards\n\n## Conclusion\n\nBuilding a strong remote team culture takes time and consistent effort, but the results are worth it. Teams with strong cultures are more engaged, productive, and resilient.`,\n    featuredImage: \"/images/team-culture.jpg\",\n    category: \"Team Management\",\n    tags: [\"team culture\", \"remote teams\", \"leadership\", \"collaboration\"],\n    author,\n    publishedAt: \"2024-01-10\",\n    readingTime: 6,\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Essential Tools for Remote Workers in 2024\",\n    slug: \"essential-tools-remote-workers-2024\",\n    excerpt: \"A comprehensive review of the best tools and software for remote work, from communication platforms to productivity apps.\",\n    content: `# Essential Tools for Remote Workers in 2024\n\nThe right tools can make or break your remote work experience. Here's a curated list of essential tools every remote worker should consider.\n\n## Communication Tools\n\n### Video Conferencing\n- **Zoom**: Industry standard for meetings\n- **Google Meet**: Integrated with Google Workspace\n- **Microsoft Teams**: Great for Microsoft ecosystem\n\n### Instant Messaging\n- **Slack**: Feature-rich team communication\n- **Discord**: Great for informal team chat\n- **Microsoft Teams**: All-in-one solution\n\n## Productivity Tools\n\n### Task Management\n- **Asana**: Project management and team collaboration\n- **Trello**: Visual project boards\n- **Notion**: All-in-one workspace\n\n### Time Tracking\n- **Toggl**: Simple time tracking\n- **RescueTime**: Automatic time tracking\n- **Clockify**: Free time tracking for teams\n\n## File Sharing and Storage\n\n### Cloud Storage\n- **Google Drive**: Integrated with Google Workspace\n- **Dropbox**: Reliable file sync\n- **OneDrive**: Microsoft ecosystem integration\n\n### Document Collaboration\n- **Google Docs**: Real-time collaboration\n- **Microsoft 365**: Comprehensive office suite\n- **Notion**: Wiki-style documentation\n\n## Security Tools\n\n### VPN Services\n- **NordVPN**: Reliable and fast\n- **ExpressVPN**: Great for streaming\n- **Surfshark**: Budget-friendly option\n\n### Password Management\n- **1Password**: User-friendly interface\n- **Bitwarden**: Open-source option\n- **LastPass**: Popular choice\n\n## Conclusion\n\nThe key is to choose tools that integrate well together and fit your team's workflow. Start with the basics and gradually add more specialized tools as needed.`,\n    featuredImage: \"/images/remote-tools.jpg\",\n    category: \"Tools & Software\",\n    tags: [\"tools\", \"software\", \"productivity\", \"remote work\"],\n    author,\n    publishedAt: \"2024-01-05\",\n    readingTime: 7,\n    featured: false\n  }\n];\n\nexport const categories = [\n  \"All\",\n  \"Productivity\",\n  \"Team Management\", \n  \"Tools & Software\",\n  \"Work-Life Balance\",\n  \"Career Development\"\n];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,SAAiB;IACrB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wPAmD0O,CAAC;QACrP,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;YAAmB;SAAiB;QAC1E;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6KAqD+J,CAAC;QAC1K,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAc;SAAgB;QACrE;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+JAsDiJ,CAAC;QAC5J,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAS;YAAY;YAAgB;SAAc;QAC1D;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/job-listings.ts"], "sourcesContent": ["import { JobListing } from '@/types/blog';\n\nexport const jobListings: JobListing[] = [\n  {\n    id: \"1\",\n    title: \"Senior Frontend Developer\",\n    company: \"TechFlow Inc.\",\n    location: \"Remote (US)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/senior-frontend-developer\",\n    postedAt: \"2024-01-15\"\n  },\n  {\n    id: \"2\", \n    title: \"Product Manager\",\n    company: \"InnovateCorp\",\n    location: \"Remote (Global)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/product-manager\",\n    postedAt: \"2024-01-14\"\n  },\n  {\n    id: \"3\",\n    title: \"UX/UI Designer\",\n    company: \"DesignStudio\",\n    location: \"Remote (EU)\",\n    type: \"Contract\",\n    remote: true,\n    url: \"https://example.com/jobs/ux-ui-designer\",\n    postedAt: \"2024-01-13\"\n  },\n  {\n    id: \"4\",\n    title: \"<PERSON><PERSON>ps Engineer\",\n    company: \"CloudTech Solutions\",\n    location: \"Remote (Americas)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/devops-engineer\",\n    postedAt: \"2024-01-12\"\n  },\n  {\n    id: \"5\",\n    title: \"Content Marketing Specialist\",\n    company: \"GrowthHackers\",\n    location: \"Remote (Worldwide)\",\n    type: \"Part-time\",\n    remote: true,\n    url: \"https://example.com/jobs/content-marketing-specialist\",\n    postedAt: \"2024-01-11\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/seo.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { BlogPost } from '@/types/blog';\n\nconst baseUrl = 'https://remotework.com';\nconst siteName = 'RemoteWork';\nconst defaultTitle = 'RemoteWork - Remote work made simple';\nconst defaultDescription = 'Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.';\n\nexport function generatePageMetadata({\n  title,\n  description,\n  path = '',\n  image = '/images/og-default.jpg',\n  keywords = [],\n  publishedTime,\n  modifiedTime,\n  authors = ['<PERSON> Johnson'],\n  type = 'website'\n}: {\n  title?: string;\n  description?: string;\n  path?: string;\n  image?: string;\n  keywords?: string[];\n  publishedTime?: string;\n  modifiedTime?: string;\n  authors?: string[];\n  type?: 'website' | 'article';\n}): Metadata {\n  const fullTitle = title ? `${title} | ${siteName}` : defaultTitle;\n  const fullDescription = description || defaultDescription;\n  const fullUrl = `${baseUrl}${path}`;\n  const fullImage = image.startsWith('http') ? image : `${baseUrl}${image}`;\n\n  const metadata: Metadata = {\n    title: fullTitle,\n    description: fullDescription,\n    keywords: keywords.length > 0 ? keywords : ['remote work', 'productivity', 'work from home', 'distributed teams'],\n    authors: authors.map(name => ({ name })),\n    creator: 'Alex <PERSON>',\n    publisher: siteName,\n    robots: {\n      index: true,\n      follow: true,\n      googleBot: {\n        index: true,\n        follow: true,\n        'max-video-preview': -1,\n        'max-image-preview': 'large',\n        'max-snippet': -1,\n      },\n    },\n    openGraph: {\n      type,\n      locale: 'en_US',\n      url: fullUrl,\n      title: fullTitle,\n      description: fullDescription,\n      siteName,\n      images: [\n        {\n          url: fullImage,\n          width: 1200,\n          height: 630,\n          alt: title || defaultTitle,\n        },\n      ],\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: fullTitle,\n      description: fullDescription,\n      creator: '@alexjohnson',\n      images: [fullImage],\n    },\n    alternates: {\n      canonical: fullUrl,\n    },\n  };\n\n  // Add article-specific metadata\n  if (type === 'article' && publishedTime) {\n    metadata.openGraph = {\n      ...metadata.openGraph,\n      type: 'article',\n      publishedTime,\n      modifiedTime: modifiedTime || publishedTime,\n      authors: authors,\n      tags: keywords,\n    };\n  }\n\n  return metadata;\n}\n\nexport function generateBlogPostMetadata(post: BlogPost): Metadata {\n  return generatePageMetadata({\n    title: post.title,\n    description: post.excerpt,\n    path: `/blog/${post.slug}`,\n    image: post.featuredImage || '/images/og-blog.jpg',\n    keywords: post.tags,\n    publishedTime: post.publishedAt,\n    authors: [post.author.name],\n    type: 'article',\n  });\n}\n\nexport function generateStructuredData(post?: BlogPost) {\n  const baseStructuredData = {\n    '@context': 'https://schema.org',\n    '@type': 'WebSite',\n    name: siteName,\n    url: baseUrl,\n    description: defaultDescription,\n    publisher: {\n      '@type': 'Person',\n      name: 'Alex Johnson',\n      url: `${baseUrl}/about`,\n    },\n  };\n\n  if (post) {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'BlogPosting',\n      headline: post.title,\n      description: post.excerpt,\n      image: post.featuredImage ? `${baseUrl}${post.featuredImage}` : `${baseUrl}/images/og-blog.jpg`,\n      author: {\n        '@type': 'Person',\n        name: post.author.name,\n        url: post.author.social.website || `${baseUrl}/about`,\n      },\n      publisher: {\n        '@type': 'Person',\n        name: 'Alex Johnson',\n        url: `${baseUrl}/about`,\n      },\n      datePublished: post.publishedAt,\n      dateModified: post.publishedAt,\n      mainEntityOfPage: {\n        '@type': 'WebPage',\n        '@id': `${baseUrl}/blog/${post.slug}`,\n      },\n      keywords: post.tags.join(', '),\n      articleSection: post.category,\n      wordCount: post.content.split(' ').length,\n      timeRequired: `PT${post.readingTime}M`,\n    };\n  }\n\n  return baseStructuredData;\n}\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,qBAAqB;AAEpB,SAAS,qBAAqB,EACnC,KAAK,EACL,WAAW,EACX,OAAO,EAAE,EACT,QAAQ,wBAAwB,EAChC,WAAW,EAAE,EACb,aAAa,EACb,YAAY,EACZ,UAAU;IAAC;CAAe,EAC1B,OAAO,SAAS,EAWjB;IACC,MAAM,YAAY,QAAQ,GAAG,MAAM,GAAG,EAAE,UAAU,GAAG;IACrD,MAAM,kBAAkB,eAAe;IACvC,MAAM,UAAU,GAAG,UAAU,MAAM;IACnC,MAAM,YAAY,MAAM,UAAU,CAAC,UAAU,QAAQ,GAAG,UAAU,OAAO;IAEzE,MAAM,WAAqB;QACzB,OAAO;QACP,aAAa;QACb,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAAC;YAAe;YAAgB;YAAkB;SAAoB;QACjH,SAAS,QAAQ,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE;YAAK,CAAC;QACtC,SAAS;QACT,WAAW;QACX,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,WAAW;gBACT,OAAO;gBACP,QAAQ;gBACR,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;QACA,WAAW;YACT;YACA,QAAQ;YACR,KAAK;YACL,OAAO;YACP,aAAa;YACb;YACA,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK,SAAS;gBAChB;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;YACT,QAAQ;gBAAC;aAAU;QACrB;QACA,YAAY;YACV,WAAW;QACb;IACF;IAEA,gCAAgC;IAChC,IAAI,SAAS,aAAa,eAAe;QACvC,SAAS,SAAS,GAAG;YACnB,GAAG,SAAS,SAAS;YACrB,MAAM;YACN;YACA,cAAc,gBAAgB;YAC9B,SAAS;YACT,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAEO,SAAS,yBAAyB,IAAc;IACrD,OAAO,qBAAqB;QAC1B,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,OAAO;QACzB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;QAC1B,OAAO,KAAK,aAAa,IAAI;QAC7B,UAAU,KAAK,IAAI;QACnB,eAAe,KAAK,WAAW;QAC/B,SAAS;YAAC,KAAK,MAAM,CAAC,IAAI;SAAC;QAC3B,MAAM;IACR;AACF;AAEO,SAAS,uBAAuB,IAAe;IACpD,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,KAAK;QACL,aAAa;QACb,WAAW;YACT,SAAS;YACT,MAAM;YACN,KAAK,GAAG,QAAQ,MAAM,CAAC;QACzB;IACF;IAEA,IAAI,MAAM;QACR,OAAO;YACL,YAAY;YACZ,SAAS;YACT,UAAU,KAAK,KAAK;YACpB,aAAa,KAAK,OAAO;YACzB,OAAO,KAAK,aAAa,GAAG,GAAG,UAAU,KAAK,aAAa,EAAE,GAAG,GAAG,QAAQ,mBAAmB,CAAC;YAC/F,QAAQ;gBACN,SAAS;gBACT,MAAM,KAAK,MAAM,CAAC,IAAI;gBACtB,KAAK,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,GAAG,QAAQ,MAAM,CAAC;YACvD;YACA,WAAW;gBACT,SAAS;gBACT,MAAM;gBACN,KAAK,GAAG,QAAQ,MAAM,CAAC;YACzB;YACA,eAAe,KAAK,WAAW;YAC/B,cAAc,KAAK,WAAW;YAC9B,kBAAkB;gBAChB,SAAS;gBACT,OAAO,GAAG,QAAQ,MAAM,EAAE,KAAK,IAAI,EAAE;YACvC;YACA,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC;YACzB,gBAAgB,KAAK,QAAQ;YAC7B,WAAW,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM;YACzC,cAAc,CAAC,EAAE,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;QACxC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowR<PERSON>, Clock, User, ExternalLink, Briefcase, TrendingUp, Users, Zap } from 'lucide-react';\nimport Layout from '@/components/layout/Layout';\nimport { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { blogPosts } from '@/data/blog-posts';\nimport { jobListings } from '@/data/job-listings';\nimport { formatDate, formatRelativeDate } from '@/lib/utils';\nimport { generatePageMetadata, generateStructuredData } from '@/lib/seo';\nimport type { Metadata } from 'next';\n\nexport const metadata: Metadata = generatePageMetadata({\n  title: 'Remote work made simple',\n  description: 'Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.',\n  path: '/',\n});\n\nexport default function Home() {\n  const featuredPosts = blogPosts.filter(post => post.featured).slice(0, 3);\n  const latestJobs = jobListings.slice(0, 5);\n\n  return (\n    <Layout>\n      {/* Structured Data */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(generateStructuredData()),\n        }}\n      />\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Remote work made{' '}\n              <span className=\"text-blue-600\">simple</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" asChild>\n                <Link href=\"/blog\">\n                  Explore Articles\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" asChild>\n                <Link href=\"/about\">Learn More</Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4\">\n                <TrendingUp className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">50+</h3>\n              <p className=\"text-gray-600\">Productivity Articles</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4\">\n                <Users className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">10K+</h3>\n              <p className=\"text-gray-600\">Remote Workers Helped</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4\">\n                <Zap className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">95%</h3>\n              <p className=\"text-gray-600\">Productivity Increase</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Blog Posts */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Featured Articles</h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Discover our most popular and impactful articles on remote work, productivity, and team management.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n            {featuredPosts.map((post) => (\n              <Card key={post.id} className=\"h-full flex flex-col\">\n                <CardHeader>\n                  <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg mb-4 flex items-center justify-center\">\n                    <Briefcase className=\"h-12 w-12 text-blue-600\" />\n                  </div>\n                  <div className=\"flex items-center gap-2 text-sm text-gray-500 mb-2\">\n                    <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      {post.category}\n                    </span>\n                    <span>•</span>\n                    <span>{formatDate(post.publishedAt)}</span>\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2 line-clamp-2\">\n                    {post.title}\n                  </h3>\n                </CardHeader>\n\n                <CardContent className=\"flex-1\">\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                    {post.excerpt}\n                  </p>\n                </CardContent>\n\n                <CardFooter>\n                  <div className=\"flex items-center justify-between w-full\">\n                    <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center gap-1\">\n                        <User className=\"h-4 w-4\" />\n                        <span>{post.author.name}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"h-4 w-4\" />\n                        <span>{post.readingTime} min read</span>\n                      </div>\n                    </div>\n                    <Button variant=\"outline\" size=\"sm\" asChild>\n                      <Link href={`/blog/${post.slug}`}>\n                        Read More\n                      </Link>\n                    </Button>\n                  </div>\n                </CardFooter>\n              </Card>\n            ))}\n          </div>\n\n          <div className=\"text-center\">\n            <Button size=\"lg\" asChild>\n              <Link href=\"/blog\">\n                View All Articles\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Latest Job Opportunities */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Latest Remote Job Opportunities</h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Discover exciting remote work opportunities from top companies around the world.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <Card>\n              <CardContent>\n                <div className=\"space-y-6\">\n                  {latestJobs.map((job, index) => (\n                    <div key={job.id} className={`flex items-center justify-between py-4 ${index !== latestJobs.length - 1 ? 'border-b border-gray-100' : ''}`}>\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{job.title}</h3>\n                          <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\">\n                            {job.type}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                          <span className=\"font-medium\">{job.company}</span>\n                          <span>•</span>\n                          <span>{job.location}</span>\n                          <span>•</span>\n                          <span>{formatRelativeDate(job.postedAt)}</span>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-3\">\n                        <Button variant=\"outline\" size=\"sm\" asChild>\n                          <a href={job.url} target=\"_blank\" rel=\"noopener noreferrer\">\n                            Apply Now\n                            <ExternalLink className=\"ml-1 h-4 w-4\" />\n                          </a>\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            <div className=\"text-center mt-8\">\n              <Button variant=\"outline\" size=\"lg\">\n                View More Jobs\n                <ExternalLink className=\"ml-2 h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter CTA */}\n      <section className=\"py-20 bg-blue-600\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-2xl mx-auto text-center\">\n            <h2 className=\"text-3xl font-bold text-white mb-4\">\n              Stay Updated with Remote Work Trends\n            </h2>\n            <p className=\"text-blue-100 mb-8 text-lg\">\n              Get weekly insights, productivity tips, and job opportunities delivered to your inbox.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"form-input flex-1\"\n              />\n              <Button variant=\"secondary\" size=\"lg\">\n                Subscribe\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAGO,MAAM,WAAqB,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;IACrD,OAAO;IACP,aAAa;IACb,MAAM;AACR;AAEe,SAAS;IACtB,MAAM,gBAAgB,4HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,KAAK,CAAC,GAAG;IACvE,MAAM,aAAa,8HAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;IAExC,qBACE,8OAAC,sIAAA,CAAA,UAAM;;0BAEL,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,CAAA,GAAA,iHAAA,CAAA,yBAAsB,AAAD;gBAC9C;;;;;;0BAIF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;oCAC/C;kDACjB,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,OAAO;kDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAQ;8DAEjB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,OAAO;kDACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,gIAAA,CAAA,OAAI;oCAAe,WAAU;;sDAC5B,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,QAAQ;;;;;;sEAEhB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;8DAEpC,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;;;;;;;sDAIf,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;;;;;;sDAIjB,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAM,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;;4EAAM,KAAK,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;kEAG5B,8OAAC,kIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,OAAO;kEACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;sEAAE;;;;;;;;;;;;;;;;;;;;;;;mCApC/B,KAAK,EAAE;;;;;;;;;;sCA8CtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAK,OAAO;0CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;wCAAQ;sDAEjB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,KAAK,sBACpB,8OAAC;oDAAiB,WAAW,CAAC,uCAAuC,EAAE,UAAU,WAAW,MAAM,GAAG,IAAI,6BAA6B,IAAI;;sEACxI,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAuC,IAAI,KAAK;;;;;;sFAC9D,8OAAC;4EAAK,WAAU;sFACb,IAAI,IAAI;;;;;;;;;;;;8EAGb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAe,IAAI,OAAO;;;;;;sFAC1C,8OAAC;sFAAK;;;;;;sFACN,8OAAC;sFAAM,IAAI,QAAQ;;;;;;sFACnB,8OAAC;sFAAK;;;;;;sFACN,8OAAC;sFAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,QAAQ;;;;;;;;;;;;;;;;;;sEAG1C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,OAAO;0EACzC,cAAA,8OAAC;oEAAE,MAAM,IAAI,GAAG;oEAAE,QAAO;oEAAS,KAAI;;wEAAsB;sFAE1D,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDApBtB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;8CA8BxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;;4CAAK;0DAElC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAY,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}]}