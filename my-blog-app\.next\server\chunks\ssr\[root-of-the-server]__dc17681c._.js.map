{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;IACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;IACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;IACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;AACpD;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'btn inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'btn-primary focus:ring-blue-500',\n      secondary: 'btn-secondary focus:ring-gray-500',\n      outline: 'btn-outline focus:ring-blue-500',\n      ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Menu, X, Home, BookOpen, User, Mail, Briefcase } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nconst navigation = [\n  { name: 'Home', href: '/', icon: Home },\n  { name: 'Blog', href: '/blog', icon: BookOpen },\n  { name: 'About', href: '/about', icon: User },\n  { name: 'Contact', href: '/contact', icon: Mail },\n];\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n                <Briefcase className=\"h-5 w-5\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">RemoteWork</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  )}\n                >\n                  <item.icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\" asChild>\n              <Link href=\"/blog\">Latest Posts</Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {isMenuOpen ? (\n                <X className=\"block h-6 w-6\" />\n              ) : (\n                <Menu className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={cn(\n                      'flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors',\n                      isActive\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                    )}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    <span>{item.name}</span>\n                  </Link>\n                );\n              })}\n              <div className=\"pt-4 pb-2\">\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\" asChild>\n                  <Link href=\"/blog\">Latest Posts</Link>\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;IAAC;CACjD;AAED,MAAM,SAAmB;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,WACI,6BACA;;sDAGN,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCAVX,KAAK,IAAI;;;;;4BAapB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,OAAO;0CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAQ;;;;;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,2BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,WACI,6BACA;oCAEN,SAAS,IAAM,cAAc;;sDAE7B,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCAXX,KAAK,IAAI;;;;;4BAcpB;0CACA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;uCAEe", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/blog/CommentSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { MessageCircle, Reply, User, Calendar } from 'lucide-react';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { Comment } from '@/types/blog';\nimport { formatRelativeDate } from '@/lib/utils';\n\ninterface CommentSectionProps {\n  postId: string;\n}\n\n// Mock comments data - in a real app, this would come from an API\nconst mockComments: Comment[] = [\n  {\n    id: '1',\n    postId: '1',\n    author: '<PERSON>',\n    email: '<EMAIL>',\n    content: 'This is such a comprehensive guide! I especially loved the section about creating a dedicated workspace. It made a huge difference in my productivity.',\n    createdAt: '2024-01-16',\n    replies: [\n      {\n        id: '2',\n        postId: '1',\n        author: '<PERSON>',\n        email: '<EMAIL>',\n        content: 'I agree! Having a dedicated space really helps with the mental separation between work and personal time.',\n        createdAt: '2024-01-16',\n      }\n    ]\n  },\n  {\n    id: '3',\n    postId: '1',\n    author: '<PERSON>',\n    email: '<EMAIL>',\n    content: 'The Pomodoro Technique has been a game-changer for me. Thanks for the detailed explanation of how to implement it effectively!',\n    createdAt: '2024-01-17',\n  }\n];\n\nconst CommentSection: React.FC<CommentSectionProps> = ({ postId }) => {\n  const [comments, setComments] = useState<Comment[]>(mockComments.filter(c => c.postId === postId));\n  const [newComment, setNewComment] = useState({ name: '', email: '', content: '' });\n  const [replyingTo, setReplyingTo] = useState<string | null>(null);\n  const [replyContent, setReplyContent] = useState('');\n\n  const handleSubmitComment = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newComment.name || !newComment.email || !newComment.content) return;\n\n    const comment: Comment = {\n      id: Date.now().toString(),\n      postId,\n      author: newComment.name,\n      email: newComment.email,\n      content: newComment.content,\n      createdAt: new Date().toISOString().split('T')[0],\n    };\n\n    setComments([...comments, comment]);\n    setNewComment({ name: '', email: '', content: '' });\n  };\n\n  const handleSubmitReply = (parentId: string) => {\n    if (!replyContent.trim()) return;\n\n    const reply: Comment = {\n      id: Date.now().toString(),\n      postId,\n      author: 'Anonymous', // In a real app, this would come from auth\n      email: '<EMAIL>',\n      content: replyContent,\n      createdAt: new Date().toISOString().split('T')[0],\n    };\n\n    setComments(comments.map(comment => {\n      if (comment.id === parentId) {\n        return {\n          ...comment,\n          replies: [...(comment.replies || []), reply]\n        };\n      }\n      return comment;\n    }));\n\n    setReplyContent('');\n    setReplyingTo(null);\n  };\n\n  const CommentItem: React.FC<{ comment: Comment; isReply?: boolean }> = ({ comment, isReply = false }) => (\n    <div className={`${isReply ? 'ml-8 mt-4' : 'mb-6'}`}>\n      <Card className={`${isReply ? 'bg-gray-50' : ''}`}>\n        <CardContent className=\"pt-4\">\n          <div className=\"flex items-start gap-3\">\n            <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n              <User className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <h4 className=\"font-semibold text-gray-900\">{comment.author}</h4>\n                <div className=\"flex items-center gap-1 text-sm text-gray-500\">\n                  <Calendar className=\"h-3 w-3\" />\n                  <span>{formatRelativeDate(comment.createdAt)}</span>\n                </div>\n              </div>\n              <p className=\"text-gray-700 mb-3 leading-relaxed\">{comment.content}</p>\n              {!isReply && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}\n                >\n                  <Reply className=\"h-4 w-4 mr-1\" />\n                  Reply\n                </Button>\n              )}\n            </div>\n          </div>\n          \n          {/* Reply Form */}\n          {replyingTo === comment.id && (\n            <div className=\"mt-4 ml-13\">\n              <div className=\"flex gap-2\">\n                <textarea\n                  value={replyContent}\n                  onChange={(e) => setReplyContent(e.target.value)}\n                  placeholder=\"Write a reply...\"\n                  className=\"form-input form-textarea flex-1\"\n                  rows={3}\n                />\n              </div>\n              <div className=\"flex gap-2 mt-2\">\n                <Button size=\"sm\" onClick={() => handleSubmitReply(comment.id)}>\n                  Post Reply\n                </Button>\n                <Button variant=\"outline\" size=\"sm\" onClick={() => setReplyingTo(null)}>\n                  Cancel\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n      \n      {/* Replies */}\n      {comment.replies?.map((reply) => (\n        <CommentItem key={reply.id} comment={reply} isReply />\n      ))}\n    </div>\n  );\n\n  return (\n    <section className=\"py-12 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"flex items-center gap-2 mb-8\">\n            <MessageCircle className=\"h-6 w-6 text-blue-600\" />\n            <h2 className=\"text-2xl font-bold text-gray-900\">\n              Comments ({comments.length})\n            </h2>\n          </div>\n\n          {/* Comment Form */}\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Leave a Comment</h3>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmitComment} className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      value={newComment.name}\n                      onChange={(e) => setNewComment({ ...newComment, name: e.target.value })}\n                      className=\"form-input\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Email *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      value={newComment.email}\n                      onChange={(e) => setNewComment({ ...newComment, email: e.target.value })}\n                      className=\"form-input\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div>\n                  <label htmlFor=\"content\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Comment *\n                  </label>\n                  <textarea\n                    id=\"content\"\n                    value={newComment.content}\n                    onChange={(e) => setNewComment({ ...newComment, content: e.target.value })}\n                    className=\"form-input form-textarea\"\n                    rows={4}\n                    placeholder=\"Share your thoughts...\"\n                    required\n                  />\n                </div>\n                <Button type=\"submit\">\n                  Post Comment\n                </Button>\n              </form>\n            </CardContent>\n          </Card>\n\n          {/* Comments List */}\n          <div>\n            {comments.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <MessageCircle className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No comments yet</h3>\n                <p className=\"text-gray-600\">Be the first to share your thoughts!</p>\n              </div>\n            ) : (\n              comments.map((comment) => (\n                <CommentItem key={comment.id} comment={comment} />\n              ))\n            )}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CommentSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAPA;;;;;;;AAaA,kEAAkE;AAClE,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,SAAS;QACT,WAAW;QACX,SAAS;YACP;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,SAAS;gBACT,WAAW;YACb;SACD;IACH;IACA;QACE,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,OAAO;QAAI,SAAS;IAAG;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,OAAO,EAAE;QAElE,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,QAAQ,WAAW,IAAI;YACvB,OAAO,WAAW,KAAK;YACvB,SAAS,WAAW,OAAO;YAC3B,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD;QAEA,YAAY;eAAI;YAAU;SAAQ;QAClC,cAAc;YAAE,MAAM;YAAI,OAAO;YAAI,SAAS;QAAG;IACnD;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,aAAa,IAAI,IAAI;QAE1B,MAAM,QAAiB;YACrB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,QAAQ;YACR,OAAO;YACP,SAAS;YACT,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD;QAEA,YAAY,SAAS,GAAG,CAAC,CAAA;YACvB,IAAI,QAAQ,EAAE,KAAK,UAAU;gBAC3B,OAAO;oBACL,GAAG,OAAO;oBACV,SAAS;2BAAK,QAAQ,OAAO,IAAI,EAAE;wBAAG;qBAAM;gBAC9C;YACF;YACA,OAAO;QACT;QAEA,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,cAAiE,CAAC,EAAE,OAAO,EAAE,UAAU,KAAK,EAAE,iBAClG,8OAAC;YAAI,WAAW,GAAG,UAAU,cAAc,QAAQ;;8BACjD,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAW,GAAG,UAAU,eAAe,IAAI;8BAC/C,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+B,QAAQ,MAAM;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;;;;;;;0DAG/C,8OAAC;gDAAE,WAAU;0DAAsC,QAAQ,OAAO;;;;;;4CACjE,CAAC,yBACA,8OAAC,kIAAA,CAAA,UAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,cAAc,eAAe,QAAQ,EAAE,GAAG,OAAO,QAAQ,EAAE;;kEAE1E,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAQzC,eAAe,QAAQ,EAAE,kBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,aAAY;4CACZ,WAAU;4CACV,MAAM;;;;;;;;;;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,UAAM;gDAAC,MAAK;gDAAK,SAAS,IAAM,kBAAkB,QAAQ,EAAE;0DAAG;;;;;;0DAGhE,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,SAAS,IAAM,cAAc;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUjF,QAAQ,OAAO,EAAE,IAAI,CAAC,sBACrB,8OAAC;wBAA2B,SAAS;wBAAO,OAAO;uBAAjC,MAAM,EAAE;;;;;;;;;;;IAKhC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;;oCAAmC;oCACpC,SAAS,MAAM;oCAAC;;;;;;;;;;;;;kCAK/B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAEtD,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAK,UAAU;oCAAqB,WAAU;;sDAC7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,WAAW,IAAI;4DACtB,UAAU,CAAC,IAAM,cAAc;oEAAE,GAAG,UAAU;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACrE,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,OAAO,WAAW,KAAK;4DACvB,UAAU,CAAC,IAAM,cAAc;oEAAE,GAAG,UAAU;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACtE,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,OAAO,WAAW,OAAO;oDACzB,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACxE,WAAU;oDACV,MAAM;oDACN,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC,kIAAA,CAAA,UAAM;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,8OAAC;kCACE,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;uEAG/B,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;gCAA6B,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;uCAEe", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/blog/SocialShareButtons.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Twitter, Linkedin, Share2 } from 'lucide-react';\nimport Button from '@/components/ui/Button';\n\ninterface SocialShareButtonsProps {\n  url: string;\n  title: string;\n}\n\nconst SocialShareButtons: React.FC<SocialShareButtonsProps> = ({ url, title }) => {\n  const shareText = `Check out this article: ${title}`;\n\n  const handleShare = (platform: 'twitter' | 'linkedin') => {\n    const urls = {\n      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(url)}`,\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`\n    };\n    \n    window.open(urls[platform], '_blank', 'width=600,height=400');\n  };\n\n  const handleNativeShare = () => {\n    if (navigator.share) {\n      navigator.share({ title, url });\n    } else {\n      handleShare('twitter');\n    }\n  };\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <span className=\"text-sm text-gray-600 mr-2\">Share:</span>\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => handleShare('twitter')}\n      >\n        <Twitter className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => handleShare('linkedin')}\n      >\n        <Linkedin className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={handleNativeShare}\n      >\n        <Share2 className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n};\n\nexport default SocialShareButtons;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAWA,MAAM,qBAAwD,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;IAC3E,MAAM,YAAY,CAAC,wBAAwB,EAAE,OAAO;IAEpD,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO;YACX,SAAS,CAAC,sCAAsC,EAAE,mBAAmB,WAAW,KAAK,EAAE,mBAAmB,MAAM;YAChH,UAAU,CAAC,oDAAoD,EAAE,mBAAmB,MAAM;QAC5F;QAEA,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU;IACxC;IAEA,MAAM,oBAAoB;QACxB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBAAE;gBAAO;YAAI;QAC/B,OAAO;YACL,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BAA6B;;;;;;0BAC7C,8OAAC,kIAAA,CAAA,UAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,YAAY;0BAE3B,cAAA,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAErB,8OAAC,kIAAA,CAAA,UAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,YAAY;0BAE3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAEtB,8OAAC,kIAAA,CAAA,UAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;0BAET,cAAA,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1B;uCAEe", "debugId": null}}]}