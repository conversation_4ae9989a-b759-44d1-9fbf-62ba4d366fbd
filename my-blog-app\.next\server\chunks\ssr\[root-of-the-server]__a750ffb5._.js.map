{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/admin/AdminLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/admin/AdminLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/AdminLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/admin/AdminLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/admin/AdminLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/admin/AdminLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;IACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;IACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;IACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;AACpD;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;AAOA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'btn inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'btn-primary focus:ring-blue-500',\n      secondary: 'btn-secondary focus:ring-gray-500',\n      outline: 'btn-outline focus:ring-blue-500',\n      ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/blog-posts.ts"], "sourcesContent": ["import { Blog<PERSON><PERSON>, Author } from '@/types/blog';\n\nconst author: Author = {\n  name: \"<PERSON>\",\n  bio: \"Remote work consultant and productivity expert with 8+ years of experience helping teams transition to distributed work environments.\",\n  avatar: \"/images/author-avatar.jpg\",\n  social: {\n    twitter: \"https://twitter.com/alex<PERSON><PERSON><PERSON>\",\n    linkedin: \"https://linkedin.com/in/alex<PERSON><PERSON><PERSON>\",\n    website: \"https://alexjohnson.dev\"\n  }\n};\n\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"The Ultimate Guide to Remote Work Productivity\",\n    slug: \"ultimate-guide-remote-work-productivity\",\n    excerpt: \"Discover proven strategies and tools to maximize your productivity while working from home. Learn how to create the perfect remote work environment.\",\n    content: `# The Ultimate Guide to Remote Work Productivity\n\nWorking remotely has become the new normal for millions of professionals worldwide. However, maintaining productivity while working from home presents unique challenges that require specific strategies and tools.\n\n## Creating Your Ideal Workspace\n\nYour physical environment plays a crucial role in your productivity. Here are key elements to consider:\n\n### 1. Dedicated Work Area\n- Choose a quiet space away from distractions\n- Ensure good lighting, preferably natural light\n- Invest in ergonomic furniture\n\n### 2. Essential Tools and Equipment\n- High-quality monitor and keyboard\n- Reliable internet connection\n- Noise-canceling headphones\n- Good webcam for video calls\n\n## Time Management Strategies\n\n### The Pomodoro Technique\nWork in focused 25-minute intervals followed by 5-minute breaks. This technique helps maintain concentration and prevents burnout.\n\n### Time Blocking\nSchedule specific time blocks for different types of work:\n- Deep work sessions\n- Meetings and calls\n- Email and communication\n- Administrative tasks\n\n## Communication Best Practices\n\nEffective communication is vital for remote teams:\n\n- **Over-communicate**: Share updates proactively\n- **Use the right tools**: Choose appropriate channels for different types of communication\n- **Set boundaries**: Establish clear working hours\n- **Regular check-ins**: Schedule consistent team meetings\n\n## Maintaining Work-Life Balance\n\nRemote work can blur the lines between personal and professional life. Here's how to maintain balance:\n\n1. **Set clear boundaries** between work and personal time\n2. **Create rituals** to start and end your workday\n3. **Take regular breaks** and step away from your workspace\n4. **Stay connected** with colleagues and friends\n\n## Conclusion\n\nRemote work productivity isn't just about having the right tools—it's about developing the right habits and mindset. By implementing these strategies consistently, you'll find yourself more productive and satisfied with your remote work experience.`,\n    featuredImage: \"/images/remote-productivity.jpg\",\n    category: \"Productivity\",\n    tags: [\"remote work\", \"productivity\", \"time management\", \"work from home\"],\n    author,\n    publishedAt: \"2024-01-15\",\n    readingTime: 8,\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Building a Strong Remote Team Culture\",\n    slug: \"building-strong-remote-team-culture\",\n    excerpt: \"Learn how to foster connection, collaboration, and culture in distributed teams. Essential strategies for remote team leaders.\",\n    content: `# Building a Strong Remote Team Culture\n\nCreating a cohesive team culture in a remote environment requires intentional effort and the right strategies. Here's how to build and maintain a strong remote team culture.\n\n## Foundation of Remote Culture\n\n### Clear Values and Mission\n- Define your team's core values\n- Communicate the mission regularly\n- Align individual goals with team objectives\n\n### Trust and Transparency\n- Foster open communication\n- Share information freely\n- Trust team members to manage their time\n\n## Virtual Team Building Activities\n\n### Regular Social Interactions\n- Virtual coffee chats\n- Online team games\n- Shared interest groups\n- Virtual lunch meetings\n\n### Collaborative Projects\n- Cross-functional initiatives\n- Mentorship programs\n- Knowledge sharing sessions\n\n## Communication Strategies\n\n### Structured Communication\n- Daily standups\n- Weekly team meetings\n- Monthly all-hands meetings\n- Quarterly reviews\n\n### Informal Communication\n- Slack channels for casual chat\n- Virtual water cooler conversations\n- Peer recognition programs\n\n## Recognition and Celebration\n\nCelebrating wins and recognizing contributions is crucial for remote teams:\n\n- Public recognition in team meetings\n- Peer-to-peer appreciation systems\n- Virtual celebration events\n- Achievement badges or rewards\n\n## Conclusion\n\nBuilding a strong remote team culture takes time and consistent effort, but the results are worth it. Teams with strong cultures are more engaged, productive, and resilient.`,\n    featuredImage: \"/images/team-culture.jpg\",\n    category: \"Team Management\",\n    tags: [\"team culture\", \"remote teams\", \"leadership\", \"collaboration\"],\n    author,\n    publishedAt: \"2024-01-10\",\n    readingTime: 6,\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Essential Tools for Remote Workers in 2024\",\n    slug: \"essential-tools-remote-workers-2024\",\n    excerpt: \"A comprehensive review of the best tools and software for remote work, from communication platforms to productivity apps.\",\n    content: `# Essential Tools for Remote Workers in 2024\n\nThe right tools can make or break your remote work experience. Here's a curated list of essential tools every remote worker should consider.\n\n## Communication Tools\n\n### Video Conferencing\n- **Zoom**: Industry standard for meetings\n- **Google Meet**: Integrated with Google Workspace\n- **Microsoft Teams**: Great for Microsoft ecosystem\n\n### Instant Messaging\n- **Slack**: Feature-rich team communication\n- **Discord**: Great for informal team chat\n- **Microsoft Teams**: All-in-one solution\n\n## Productivity Tools\n\n### Task Management\n- **Asana**: Project management and team collaboration\n- **Trello**: Visual project boards\n- **Notion**: All-in-one workspace\n\n### Time Tracking\n- **Toggl**: Simple time tracking\n- **RescueTime**: Automatic time tracking\n- **Clockify**: Free time tracking for teams\n\n## File Sharing and Storage\n\n### Cloud Storage\n- **Google Drive**: Integrated with Google Workspace\n- **Dropbox**: Reliable file sync\n- **OneDrive**: Microsoft ecosystem integration\n\n### Document Collaboration\n- **Google Docs**: Real-time collaboration\n- **Microsoft 365**: Comprehensive office suite\n- **Notion**: Wiki-style documentation\n\n## Security Tools\n\n### VPN Services\n- **NordVPN**: Reliable and fast\n- **ExpressVPN**: Great for streaming\n- **Surfshark**: Budget-friendly option\n\n### Password Management\n- **1Password**: User-friendly interface\n- **Bitwarden**: Open-source option\n- **LastPass**: Popular choice\n\n## Conclusion\n\nThe key is to choose tools that integrate well together and fit your team's workflow. Start with the basics and gradually add more specialized tools as needed.`,\n    featuredImage: \"/images/remote-tools.jpg\",\n    category: \"Tools & Software\",\n    tags: [\"tools\", \"software\", \"productivity\", \"remote work\"],\n    author,\n    publishedAt: \"2024-01-05\",\n    readingTime: 7,\n    featured: false\n  }\n];\n\nexport const categories = [\n  \"All\",\n  \"Productivity\",\n  \"Team Management\", \n  \"Tools & Software\",\n  \"Work-Life Balance\",\n  \"Career Development\"\n];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,SAAiB;IACrB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wPAmD0O,CAAC;QACrP,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;YAAmB;SAAiB;QAC1E;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6KAqD+J,CAAC;QAC1K,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAc;SAAgB;QACrE;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+JAsDiJ,CAAC;QAC5J,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAS;YAAY;YAAgB;SAAc;QAC1D;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/app/admin/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { FileText, Eye, MessageCircle, TrendingUp, Plus, Edit } from 'lucide-react';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { blogPosts } from '@/data/blog-posts';\nimport { formatDate, formatRelativeDate } from '@/lib/utils';\nimport Link from 'next/link';\nimport type { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'Admin Dashboard - RemoteWork',\n  description: 'Admin panel for managing RemoteWork blog content',\n  robots: {\n    index: false,\n    follow: false,\n  },\n};\n\nconst AdminDashboard: React.FC = () => {\n  const recentPosts = blogPosts.slice(0, 5);\n  const totalPosts = blogPosts.length;\n  const publishedPosts = blogPosts.length;\n  const draftPosts = 0; // In a real app, this would be calculated from actual data\n\n  const stats = [\n    {\n      name: 'Total Posts',\n      value: totalPosts,\n      icon: FileText,\n      color: 'bg-blue-500',\n      change: '+2 this week'\n    },\n    {\n      name: 'Published',\n      value: publishedPosts,\n      icon: Eye,\n      color: 'bg-green-500',\n      change: '+1 this week'\n    },\n    {\n      name: 'Drafts',\n      value: draftPosts,\n      icon: Edit,\n      color: 'bg-yellow-500',\n      change: '1 pending'\n    },\n    {\n      name: 'Comments',\n      value: 24,\n      icon: MessageCircle,\n      color: 'bg-purple-500',\n      change: '+5 today'\n    }\n  ];\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n            <p className=\"text-gray-600\">Welcome back! Here's what's happening with your blog.</p>\n          </div>\n          <Button asChild>\n            <Link href=\"/admin/posts/new\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              New Post\n            </Link>\n          </Button>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {stats.map((stat) => (\n            <Card key={stat.name}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n                    <p className=\"text-xs text-gray-500 mt-1\">{stat.change}</p>\n                  </div>\n                  <div className={`p-3 rounded-full ${stat.color}`}>\n                    <stat.icon className=\"h-6 w-6 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Recent Posts */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Recent Posts</h2>\n                <Button variant=\"outline\" size=\"sm\" asChild>\n                  <Link href=\"/admin/posts\">View All</Link>\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentPosts.map((post) => (\n                  <div key={post.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-medium text-gray-900 text-sm line-clamp-1\">\n                        {post.title}\n                      </h3>\n                      <div className=\"flex items-center gap-4 mt-1 text-xs text-gray-500\">\n                        <span>{formatDate(post.publishedAt)}</span>\n                        <span>•</span>\n                        <span>{post.category}</span>\n                        <span>•</span>\n                        <span>{post.readingTime} min read</span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Button variant=\"ghost\" size=\"sm\" asChild>\n                        <Link href={`/blog/${post.slug}`} target=\"_blank\">\n                          <Eye className=\"h-4 w-4\" />\n                        </Link>\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" asChild>\n                        <Link href={`/admin/posts/${post.id}`}>\n                          <Edit className=\"h-4 w-4\" />\n                        </Link>\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Quick Actions */}\n          <Card>\n            <CardHeader>\n              <h2 className=\"text-lg font-semibold text-gray-900\">Quick Actions</h2>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <Button className=\"w-full justify-start\" asChild>\n                  <Link href=\"/admin/posts/new\">\n                    <Plus className=\"mr-2 h-4 w-4\" />\n                    Create New Post\n                  </Link>\n                </Button>\n                <Button variant=\"outline\" className=\"w-full justify-start\" asChild>\n                  <Link href=\"/admin/posts\">\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Manage Posts\n                  </Link>\n                </Button>\n                <Button variant=\"outline\" className=\"w-full justify-start\" asChild>\n                  <Link href=\"/blog\" target=\"_blank\">\n                    <Eye className=\"mr-2 h-4 w-4\" />\n                    View Blog\n                  </Link>\n                </Button>\n                <Button variant=\"outline\" className=\"w-full justify-start\" asChild>\n                  <Link href=\"/admin/analytics\">\n                    <TrendingUp className=\"mr-2 h-4 w-4\" />\n                    View Analytics\n                  </Link>\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Activity */}\n        <Card>\n          <CardHeader>\n            <h2 className=\"text-lg font-semibold text-gray-900\">Recent Activity</h2>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3 p-3 bg-blue-50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <Plus className=\"h-4 w-4 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">New post published</p>\n                  <p className=\"text-xs text-gray-500\">The Ultimate Guide to Remote Work Productivity • 2 hours ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3 p-3 bg-green-50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                  <MessageCircle className=\"h-4 w-4 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">New comment received</p>\n                  <p className=\"text-xs text-gray-500\">Sarah Johnson commented on \"Building a Strong Remote Team Culture\" • 4 hours ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3 p-3 bg-purple-50 rounded-lg\">\n                <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-4 w-4 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">Traffic milestone reached</p>\n                  <p className=\"text-xs text-gray-500\">Your blog reached 10,000 monthly visitors • 1 day ago</p>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAEA,MAAM,iBAA2B;IAC/B,MAAM,cAAc,4HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,GAAG;IACvC,MAAM,aAAa,4HAAA,CAAA,YAAS,CAAC,MAAM;IACnC,MAAM,iBAAiB,4HAAA,CAAA,YAAS,CAAC,MAAM;IACvC,MAAM,aAAa,GAAG,2DAA2D;IAEjF,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YAC<PERSON>,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,2MAAA,CAAA,OAAI;YACV,OAAO;YACP,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,QAAQ;QACV;KACD;IAED,qBACE,8OAAC,0IAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC,kIAAA,CAAA,UAAM;4BAAC,OAAO;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAoC,KAAK,KAAK;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAA8B,KAAK,MAAM;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;sDAC9C,cAAA,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;2BATlB,KAAK,IAAI;;;;;;;;;;8BAiBxB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,OAAO;0DACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAe;;;;;;;;;;;;;;;;;;;;;;8CAIhC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,KAAK,KAAK;;;;;;0EAEb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;;;;;;kFAClC,8OAAC;kFAAK;;;;;;kFACN,8OAAC;kFAAM,KAAK,QAAQ;;;;;;kFACpB,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAM,KAAK,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,UAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,OAAO;0EACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;oEAAE,QAAO;8EACvC,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAGnB,8OAAC,kIAAA,CAAA,UAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,OAAO;0EACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;8EACnC,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CArBd,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sCAgCzB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAEtD,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,UAAM;gDAAC,WAAU;gDAAuB,OAAO;0DAC9C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIrC,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAuB,OAAO;0DAChE,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIzC,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAuB,OAAO;0DAChE,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,QAAO;;sEACxB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIpC,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAuB,OAAO;0DAChE,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnD,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;sCAEtD,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;uCAEe", "debugId": null}}]}