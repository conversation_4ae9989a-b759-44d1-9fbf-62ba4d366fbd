{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,AAAC,GAAa,OAAX,YAAW;IACzC,IAAI,aAAa,IAAI,OAAO,AAAC,GAA6B,OAA3B,KAAK,KAAK,CAAC,aAAa,IAAG;IAC1D,IAAI,aAAa,KAAK,OAAO,AAAC,GAA8B,OAA5B,KAAK,KAAK,CAAC,aAAa,KAAI;IAC5D,OAAO,AAAC,GAA+B,OAA7B,KAAK,KAAK,CAAC,aAAa,MAAK;AACzC;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'btn inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'btn-primary focus:ring-blue-500',\n      secondary: 'btn-secondary focus:ring-gray-500',\n      outline: 'btn-outline focus:ring-blue-500',\n      ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAuF;QAAtF,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACnF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  LayoutDashboard, \n  FileText, \n  Plus, \n  Settings, \n  LogOut, \n  Menu, \n  X,\n  User,\n  BarChart3\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },\n  { name: 'All Posts', href: '/admin/posts', icon: FileText },\n  { name: 'New Post', href: '/admin/posts/new', icon: Plus },\n  { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },\n  { name: 'Settings', href: '/admin/settings', icon: Settings },\n];\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0\",\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"\n      )}>\n        <div className=\"flex h-16 items-center justify-between px-6 border-b\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n              <FileText className=\"h-5 w-5\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">Admin Panel</span>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden\"\n          >\n            <X className=\"h-6 w-6 text-gray-500\" />\n          </button>\n        </div>\n\n        <nav className=\"mt-6 px-3\">\n          <div className=\"space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n        </nav>\n\n        {/* User info at bottom */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t\">\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n              <User className=\"h-4 w-4 text-blue-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-900\">Alex Johnson</p>\n              <p className=\"text-xs text-gray-500\">Administrator</p>\n            </div>\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start text-gray-700\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            Sign Out\n          </Button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 bg-white shadow-sm border-b\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden\"\n            >\n              <Menu className=\"h-6 w-6 text-gray-500\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500\">\n                Welcome back, Alex!\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAjBA;;;;;;;AAuBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM,+NAAA,CAAA,kBAAe;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAgB,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAY,MAAM;QAAoB,MAAM,qMAAA,CAAA,OAAI;IAAC;IACzD;QAAE,MAAM;QAAa,MAAM;QAAoB,MAAM,qNAAA,CAAA,YAAS;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC7D;AAED,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gIACA,cAAc,kBAAkB;;kCAEhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;kCAQ9C,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAtGM;;QAEa,qIAAA,CAAA,cAAW;;;KAFxB;uCAwGS", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;KAZM;AAmBN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MANM;AAaN,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;MANM;AAaN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANM", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/blog-posts.ts"], "sourcesContent": ["import { Blog<PERSON><PERSON>, Author } from '@/types/blog';\n\nconst author: Author = {\n  name: \"<PERSON>\",\n  bio: \"Remote work consultant and productivity expert with 8+ years of experience helping teams transition to distributed work environments.\",\n  avatar: \"/images/author-avatar.jpg\",\n  social: {\n    twitter: \"https://twitter.com/alex<PERSON><PERSON><PERSON>\",\n    linkedin: \"https://linkedin.com/in/alex<PERSON><PERSON><PERSON>\",\n    website: \"https://alexjohnson.dev\"\n  }\n};\n\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"The Ultimate Guide to Remote Work Productivity\",\n    slug: \"ultimate-guide-remote-work-productivity\",\n    excerpt: \"Discover proven strategies and tools to maximize your productivity while working from home. Learn how to create the perfect remote work environment.\",\n    content: `# The Ultimate Guide to Remote Work Productivity\n\nWorking remotely has become the new normal for millions of professionals worldwide. However, maintaining productivity while working from home presents unique challenges that require specific strategies and tools.\n\n## Creating Your Ideal Workspace\n\nYour physical environment plays a crucial role in your productivity. Here are key elements to consider:\n\n### 1. Dedicated Work Area\n- Choose a quiet space away from distractions\n- Ensure good lighting, preferably natural light\n- Invest in ergonomic furniture\n\n### 2. Essential Tools and Equipment\n- High-quality monitor and keyboard\n- Reliable internet connection\n- Noise-canceling headphones\n- Good webcam for video calls\n\n## Time Management Strategies\n\n### The Pomodoro Technique\nWork in focused 25-minute intervals followed by 5-minute breaks. This technique helps maintain concentration and prevents burnout.\n\n### Time Blocking\nSchedule specific time blocks for different types of work:\n- Deep work sessions\n- Meetings and calls\n- Email and communication\n- Administrative tasks\n\n## Communication Best Practices\n\nEffective communication is vital for remote teams:\n\n- **Over-communicate**: Share updates proactively\n- **Use the right tools**: Choose appropriate channels for different types of communication\n- **Set boundaries**: Establish clear working hours\n- **Regular check-ins**: Schedule consistent team meetings\n\n## Maintaining Work-Life Balance\n\nRemote work can blur the lines between personal and professional life. Here's how to maintain balance:\n\n1. **Set clear boundaries** between work and personal time\n2. **Create rituals** to start and end your workday\n3. **Take regular breaks** and step away from your workspace\n4. **Stay connected** with colleagues and friends\n\n## Conclusion\n\nRemote work productivity isn't just about having the right tools—it's about developing the right habits and mindset. By implementing these strategies consistently, you'll find yourself more productive and satisfied with your remote work experience.`,\n    featuredImage: \"/images/remote-productivity.jpg\",\n    category: \"Productivity\",\n    tags: [\"remote work\", \"productivity\", \"time management\", \"work from home\"],\n    author,\n    publishedAt: \"2024-01-15\",\n    readingTime: 8,\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Building a Strong Remote Team Culture\",\n    slug: \"building-strong-remote-team-culture\",\n    excerpt: \"Learn how to foster connection, collaboration, and culture in distributed teams. Essential strategies for remote team leaders.\",\n    content: `# Building a Strong Remote Team Culture\n\nCreating a cohesive team culture in a remote environment requires intentional effort and the right strategies. Here's how to build and maintain a strong remote team culture.\n\n## Foundation of Remote Culture\n\n### Clear Values and Mission\n- Define your team's core values\n- Communicate the mission regularly\n- Align individual goals with team objectives\n\n### Trust and Transparency\n- Foster open communication\n- Share information freely\n- Trust team members to manage their time\n\n## Virtual Team Building Activities\n\n### Regular Social Interactions\n- Virtual coffee chats\n- Online team games\n- Shared interest groups\n- Virtual lunch meetings\n\n### Collaborative Projects\n- Cross-functional initiatives\n- Mentorship programs\n- Knowledge sharing sessions\n\n## Communication Strategies\n\n### Structured Communication\n- Daily standups\n- Weekly team meetings\n- Monthly all-hands meetings\n- Quarterly reviews\n\n### Informal Communication\n- Slack channels for casual chat\n- Virtual water cooler conversations\n- Peer recognition programs\n\n## Recognition and Celebration\n\nCelebrating wins and recognizing contributions is crucial for remote teams:\n\n- Public recognition in team meetings\n- Peer-to-peer appreciation systems\n- Virtual celebration events\n- Achievement badges or rewards\n\n## Conclusion\n\nBuilding a strong remote team culture takes time and consistent effort, but the results are worth it. Teams with strong cultures are more engaged, productive, and resilient.`,\n    featuredImage: \"/images/team-culture.jpg\",\n    category: \"Team Management\",\n    tags: [\"team culture\", \"remote teams\", \"leadership\", \"collaboration\"],\n    author,\n    publishedAt: \"2024-01-10\",\n    readingTime: 6,\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Essential Tools for Remote Workers in 2024\",\n    slug: \"essential-tools-remote-workers-2024\",\n    excerpt: \"A comprehensive review of the best tools and software for remote work, from communication platforms to productivity apps.\",\n    content: `# Essential Tools for Remote Workers in 2024\n\nThe right tools can make or break your remote work experience. Here's a curated list of essential tools every remote worker should consider.\n\n## Communication Tools\n\n### Video Conferencing\n- **Zoom**: Industry standard for meetings\n- **Google Meet**: Integrated with Google Workspace\n- **Microsoft Teams**: Great for Microsoft ecosystem\n\n### Instant Messaging\n- **Slack**: Feature-rich team communication\n- **Discord**: Great for informal team chat\n- **Microsoft Teams**: All-in-one solution\n\n## Productivity Tools\n\n### Task Management\n- **Asana**: Project management and team collaboration\n- **Trello**: Visual project boards\n- **Notion**: All-in-one workspace\n\n### Time Tracking\n- **Toggl**: Simple time tracking\n- **RescueTime**: Automatic time tracking\n- **Clockify**: Free time tracking for teams\n\n## File Sharing and Storage\n\n### Cloud Storage\n- **Google Drive**: Integrated with Google Workspace\n- **Dropbox**: Reliable file sync\n- **OneDrive**: Microsoft ecosystem integration\n\n### Document Collaboration\n- **Google Docs**: Real-time collaboration\n- **Microsoft 365**: Comprehensive office suite\n- **Notion**: Wiki-style documentation\n\n## Security Tools\n\n### VPN Services\n- **NordVPN**: Reliable and fast\n- **ExpressVPN**: Great for streaming\n- **Surfshark**: Budget-friendly option\n\n### Password Management\n- **1Password**: User-friendly interface\n- **Bitwarden**: Open-source option\n- **LastPass**: Popular choice\n\n## Conclusion\n\nThe key is to choose tools that integrate well together and fit your team's workflow. Start with the basics and gradually add more specialized tools as needed.`,\n    featuredImage: \"/images/remote-tools.jpg\",\n    category: \"Tools & Software\",\n    tags: [\"tools\", \"software\", \"productivity\", \"remote work\"],\n    author,\n    publishedAt: \"2024-01-05\",\n    readingTime: 7,\n    featured: false\n  }\n];\n\nexport const categories = [\n  \"All\",\n  \"Productivity\",\n  \"Team Management\", \n  \"Tools & Software\",\n  \"Work-Life Balance\",\n  \"Career Development\"\n];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,SAAiB;IACrB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAoDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;YAAmB;SAAiB;QAC1E;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAsDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAc;SAAgB;QACrE;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAuDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAS;YAAY;YAAgB;SAAc;QAC1D;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/app/admin/posts/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Save, Eye, ArrowLeft, Upload, X } from 'lucide-react';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { categories } from '@/data/blog-posts';\nimport { generateSlug } from '@/lib/utils';\nimport Link from 'next/link';\n\ninterface PostFormData {\n  title: string;\n  slug: string;\n  excerpt: string;\n  content: string;\n  category: string;\n  tags: string[];\n  featuredImage: string;\n  featured: boolean;\n  status: 'draft' | 'published';\n}\n\nconst NewPostPage: React.FC = () => {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [tagInput, setTagInput] = useState('');\n  \n  const [formData, setFormData] = useState<PostFormData>({\n    title: '',\n    slug: '',\n    excerpt: '',\n    content: '',\n    category: categories[1], // Skip \"All\" category\n    tags: [],\n    featuredImage: '',\n    featured: false,\n    status: 'draft'\n  });\n\n  const handleTitleChange = (title: string) => {\n    setFormData(prev => ({\n      ...prev,\n      title,\n      slug: generateSlug(title)\n    }));\n  };\n\n  const handleAddTag = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && tagInput.trim()) {\n      e.preventDefault();\n      if (!formData.tags.includes(tagInput.trim())) {\n        setFormData(prev => ({\n          ...prev,\n          tags: [...prev.tags, tagInput.trim()]\n        }));\n      }\n      setTagInput('');\n    }\n  };\n\n  const handleRemoveTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleSubmit = async (status: 'draft' | 'published') => {\n    setIsSubmitting(true);\n    \n    try {\n      // In a real app, this would make an API call to save the post\n      const postData = {\n        ...formData,\n        status,\n        id: Date.now().toString(),\n        publishedAt: status === 'published' ? new Date().toISOString().split('T')[0] : '',\n        readingTime: Math.ceil(formData.content.split(' ').length / 200),\n        author: {\n          name: \"Alex Johnson\",\n          bio: \"Remote work consultant and productivity expert\",\n          avatar: \"/images/author-avatar.jpg\",\n          social: {\n            twitter: \"https://twitter.com/alexjohnson\",\n            linkedin: \"https://linkedin.com/in/alexjohnson\",\n            website: \"https://alexjohnson.dev\"\n          }\n        }\n      };\n\n      console.log('Saving post:', postData);\n      \n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Redirect to posts list\n      router.push('/admin/posts');\n    } catch (error) {\n      console.error('Error saving post:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <Button variant=\"ghost\" size=\"sm\" asChild>\n              <Link href=\"/admin/posts\">\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\n                Back to Posts\n              </Link>\n            </Button>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Create New Post</h1>\n              <p className=\"text-gray-600\">Write and publish a new blog post</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button \n              variant=\"outline\" \n              onClick={() => handleSubmit('draft')}\n              disabled={isSubmitting || !formData.title.trim()}\n            >\n              <Save className=\"mr-2 h-4 w-4\" />\n              Save Draft\n            </Button>\n            <Button \n              onClick={() => handleSubmit('published')}\n              disabled={isSubmitting || !formData.title.trim() || !formData.content.trim()}\n            >\n              {isSubmitting ? 'Publishing...' : 'Publish'}\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Title and Slug */}\n            <Card>\n              <CardContent className=\"p-6 space-y-4\">\n                <div>\n                  <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Title *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"title\"\n                    value={formData.title}\n                    onChange={(e) => handleTitleChange(e.target.value)}\n                    className=\"form-input text-lg font-semibold\"\n                    placeholder=\"Enter post title...\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"slug\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Slug\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"slug\"\n                    value={formData.slug}\n                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}\n                    className=\"form-input font-mono text-sm\"\n                    placeholder=\"post-url-slug\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    URL: /blog/{formData.slug || 'post-slug'}\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Excerpt */}\n            <Card>\n              <CardHeader>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Excerpt</h3>\n              </CardHeader>\n              <CardContent>\n                <textarea\n                  value={formData.excerpt}\n                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}\n                  className=\"form-input form-textarea\"\n                  rows={3}\n                  placeholder=\"Write a brief description of your post...\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  This will be shown in post previews and search results.\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Content Editor */}\n            <Card>\n              <CardHeader>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Content *</h3>\n              </CardHeader>\n              <CardContent>\n                <textarea\n                  value={formData.content}\n                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                  className=\"form-input form-textarea font-mono\"\n                  rows={20}\n                  placeholder=\"Write your post content in Markdown format...\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  You can use Markdown syntax for formatting. Preview will be available after saving.\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Post Settings */}\n            <Card>\n              <CardHeader>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Post Settings</h3>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Category\n                  </label>\n                  <select\n                    id=\"category\"\n                    value={formData.category}\n                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                    className=\"form-input\"\n                  >\n                    {categories.slice(1).map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.featured}\n                      onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}\n                      className=\"rounded border-gray-300 mr-2\"\n                    />\n                    <span className=\"text-sm font-medium text-gray-700\">Featured Post</span>\n                  </label>\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Featured posts appear on the homepage\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Tags */}\n            <Card>\n              <CardHeader>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Tags</h3>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={tagInput}\n                  onChange={(e) => setTagInput(e.target.value)}\n                  onKeyDown={handleAddTag}\n                  className=\"form-input\"\n                  placeholder=\"Add a tag and press Enter\"\n                />\n                \n                <div className=\"flex flex-wrap gap-2\">\n                  {formData.tags.map((tag) => (\n                    <span\n                      key={tag}\n                      className=\"inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\"\n                    >\n                      {tag}\n                      <button\n                        onClick={() => handleRemoveTag(tag)}\n                        className=\"hover:text-blue-600\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </span>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Featured Image */}\n            <Card>\n              <CardHeader>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Featured Image</h3>\n              </CardHeader>\n              <CardContent>\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                  <Upload className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-sm text-gray-600 mb-2\">Upload featured image</p>\n                  <Button variant=\"outline\" size=\"sm\">\n                    Choose File\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default NewPostPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAwBA,MAAM,cAAwB;;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU,+HAAA,CAAA,aAAU,CAAC,EAAE;QACvB,MAAM,EAAE;QACR,eAAe;QACf,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;YACrB,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,EAAE,GAAG,KAAK,WAAW,SAAS,IAAI,IAAI;YACxC,EAAE,cAAc;YAChB,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;gBAC5C,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,MAAM;+BAAI,KAAK,IAAI;4BAAE,SAAS,IAAI;yBAAG;oBACvC,CAAC;YACH;YACA,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAEhB,IAAI;YACF,8DAA8D;YAC9D,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX;gBACA,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,aAAa,WAAW,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC/E,aAAa,KAAK,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,GAAG;gBAC5D,QAAQ;oBACN,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,QAAQ;wBACN,SAAS;wBACT,UAAU;wBACV,SAAS;oBACX;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yBAAyB;YACzB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAW;kBACV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,OAAO;8CACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI1C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU,gBAAgB,CAAC,SAAS,KAAK,CAAC,IAAI;;sDAE9C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAS,IAAM,aAAa;oCAC5B,UAAU,gBAAgB,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI;8CAEzE,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;8BAKxC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAA+C;;;;;;kEAGhF,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvE,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDAAE,WAAU;;4DAA6B;4DAC5B,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;;8CAOrC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDACC,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC1E,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;8DAEd,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAO9C,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDACC,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC1E,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;8DAEd,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAQhD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAA+C;;;;;;sEAGnF,6LAAC;4DACC,IAAG;4DACH,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC3E,WAAU;sEAET,+HAAA,CAAA,aAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,yBACxB,6LAAC;oEAAsB,OAAO;8EAC3B;mEADU;;;;;;;;;;;;;;;;8DAOnB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,SAAS,QAAQ;oEAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,UAAU,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAC7E,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;;;;;;;sEAEtD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;8CAQhD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAW;oDACX,WAAU;oDACV,aAAY;;;;;;8DAGd,6LAAC;oDAAI,WAAU;8DACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,6LAAC;4DAEC,WAAU;;gEAET;8EACD,6LAAC;oEACC,SAAS,IAAM,gBAAgB;oEAC/B,WAAU;8EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;2DARV;;;;;;;;;;;;;;;;;;;;;;8CAiBf,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,6LAAC,qIAAA,CAAA,UAAM;wDAAC,SAAQ;wDAAU,MAAK;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GArSM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCAuSS", "debugId": null}}]}