{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,AAAC,GAAa,OAAX,YAAW;IACzC,IAAI,aAAa,IAAI,OAAO,AAAC,GAA6B,OAA3B,KAAK,KAAK,CAAC,aAAa,IAAG;IAC1D,IAAI,aAAa,KAAK,OAAO,AAAC,GAA8B,OAA5B,KAAK,KAAK,CAAC,aAAa,KAAI;IAC5D,OAAO,AAAC,GAA+B,OAA7B,KAAK,KAAK,CAAC,aAAa,MAAK;AACzC;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg border whitespace-nowrap overflow-hidden text-ellipsis max-w-full';\n\n    const variants = {\n      primary: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:border-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-100 text-gray-900 border-gray-300 hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500',\n      outline: 'bg-transparent text-blue-600 border-gray-300 hover:bg-blue-50 hover:border-blue-300 focus:ring-blue-500',\n      ghost: 'bg-transparent text-gray-700 border-transparent hover:bg-gray-100 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm min-h-[32px]',\n      md: 'px-4 py-2 text-sm min-h-[40px]',\n      lg: 'px-6 py-3 text-base min-h-[48px]'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAuF;QAAtF,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACnF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Menu, X, Home, BookOpen, User, Mail, Briefcase } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\nconst navigation = [\n  { name: 'Home', href: '/', icon: Home },\n  { name: 'Blog', href: '/blog', icon: BookOpen },\n  { name: 'About', href: '/about', icon: User },\n  { name: 'Contact', href: '/contact', icon: Mail },\n];\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n                <Briefcase className=\"h-5 w-5\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">RemoteWork</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                  )}\n                >\n                  <item.icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\" asChild>\n              <Link href=\"/blog\">Latest Posts</Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {isMenuOpen ? (\n                <X className=\"block h-6 w-6\" />\n              ) : (\n                <Menu className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={cn(\n                      'flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors',\n                      isActive\n                        ? 'text-blue-600 bg-blue-50'\n                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                    )}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    <span>{item.name}</span>\n                  </Link>\n                );\n              })}\n              <div className=\"pt-4 pb-2\">\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full\" asChild>\n                  <Link href=\"/blog\">Latest Posts</Link>\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;IAAC;CACjD;AAED,MAAM,SAAmB;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0FACA,WACI,6BACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCAVX,KAAK,IAAI;;;;;4BAapB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,OAAO;0CACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAQ;;;;;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,2BACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,WACI,6BACA;oCAEN,SAAS,IAAM,cAAc;;sDAE7B,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCAXX,KAAK,IAAI;;;;;4BAcpB;0CACA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GAjGM;;QAEa,qIAAA,CAAA,cAAW;;;KAFxB;uCAmGS", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Twitter, Linkedin, Mail, Briefcase } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-50 border-t\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n                <Briefcase className=\"h-5 w-5\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">RemoteWork</span>\n            </div>\n            <p className=\"text-gray-600 mb-4 max-w-md\">\n              Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"https://linkedin.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Linkedin className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n              >\n                <Mail className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/blog\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Blog\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n              Legal\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Privacy Policy\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                  Terms of Service\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-500 text-sm\">\n              © {currentYear} RemoteWork. All rights reserved.\n            </p>\n            <p className=\"text-gray-500 text-sm mt-2 md:mt-0\">\n              Made with ❤️ for remote workers everywhere\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n// \"use client\"\n// import Link from 'next/link';\n// import { Twitter, Linkedin, Mail, Briefcase } from 'lucide-react';\n\n// const Footer: React.FC = () => {\n//   const currentYear = new Date().getFullYear();\n\n//   return (\n//     <footer className=\"bg-gray-50 border-t\">\n//       <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n//         <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n//           {/* Brand */}\n//           <div className=\"col-span-1 md:col-span-2\">\n//             <div className=\"flex items-center space-x-2 mb-4\">\n//               <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n//                 <Briefcase className=\"h-5 w-5\" />\n//               </div>\n//               <span className=\"text-xl font-bold text-gray-900\">RemoteWork</span>\n//             </div>\n//             <p className=\"text-gray-600 mb-4 max-w-md\">\n//               Your trusted resource for remote work tips, productivity strategies, and the latest opportunities in the distributed work landscape.\n//             </p>\n//             <div className=\"flex space-x-4\">\n//               <a\n//                 href=\"https://twitter.com\"\n//                 target=\"_blank\"\n//                 rel=\"noopener noreferrer\"\n//                 className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n//               >\n//                 <Twitter className=\"h-5 w-5\" />\n//               </a>\n//               <a\n//                 href=\"https://linkedin.com\"\n//                 target=\"_blank\"\n//                 rel=\"noopener noreferrer\"\n//                 className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n//               >\n//                 <Linkedin className=\"h-5 w-5\" />\n//               </a>\n//               <a\n//                 href=\"mailto:<EMAIL>\"\n//                 className=\"text-gray-400 hover:text-blue-500 transition-colors\"\n//               >\n//                 <Mail className=\"h-5 w-5\" />\n//               </a>\n//             </div>\n//           </div>\n\n//           {/* Quick Links */}\n//           <div>\n//             <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n//               Quick Links\n//             </h3>\n//             <ul className=\"space-y-2\">\n//               <li>\n//                 <Link href=\"/\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   Home\n//                 </Link>\n//               </li>\n//               <li>\n//                 <Link href=\"/blog\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   Blog\n//                 </Link>\n//               </li>\n//               <li>\n//                 <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   About\n//                 </Link>\n//               </li>\n//               <li>\n//                 <Link href=\"/contact\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   Contact\n//                 </Link>\n//               </li>\n//             </ul>\n//           </div>\n\n//           {/* Legal */}\n//           <div>\n//             <h3 className=\"text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4\">\n//               Legal\n//             </h3>\n//             <ul className=\"space-y-2\">\n//               <li>\n//                 <Link href=\"/privacy\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   Privacy Policy\n//                 </Link>\n//               </li>\n//               <li>\n//                 <Link href=\"/terms\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n//                   Terms of Service\n//                 </Link>\n//               </li>\n//             </ul>\n//           </div>\n//         </div>\n\n//         {/* Bottom Bar */}\n//         <div className=\"mt-8 pt-8 border-t border-gray-200\">\n//           <div className=\"flex flex-col md:flex-row justify-between items-center\">\n//             <p className=\"text-gray-500 text-sm\">\n//               © {currentYear} RemoteWork. All rights reserved.\n//             </p>\n//             <p className=\"text-gray-500 text-sm mt-2 md:mt-0\">\n//               Made with ❤️ for remote workers everywhere\n//             </p>\n//           </div>\n//         </div>\n//       </div>\n//     </footer>\n//   );\n// };\n\n// export default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAsD;;;;;;;;;;;sDAIjF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAsD;;;;;;;;;;;sDAIrF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;sDAItF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;sCAQ5F,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAsD;;;;;;;;;;;sDAIxF,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5F,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAwB;oCAChC;oCAAY;;;;;;;0CAEjB,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;KA3GM;uCA6GS;;;;;;CACf,eAAe;CACf,gCAAgC;CAChC,qEAAqE;CAErE,mCAAmC;CACnC,kDAAkD;CAElD,aAAa;CACb,+CAA+C;CAC/C,uEAAuE;CACvE,kEAAkE;CAClE,0BAA0B;CAC1B,uDAAuD;CACvD,iEAAiE;CACjE,6GAA6G;CAC7G,oDAAoD;CACpD,uBAAuB;CACvB,oFAAoF;CACpF,qBAAqB;CACrB,0DAA0D;CAC1D,qJAAqJ;CACrJ,mBAAmB;CACnB,+CAA+C;CAC/C,mBAAmB;CACnB,6CAA6C;CAC7C,kCAAkC;CAClC,4CAA4C;CAC5C,kFAAkF;CAClF,kBAAkB;CAClB,kDAAkD;CAClD,qBAAqB;CACrB,mBAAmB;CACnB,8CAA8C;CAC9C,kCAAkC;CAClC,4CAA4C;CAC5C,kFAAkF;CAClF,kBAAkB;CAClB,mDAAmD;CACnD,qBAAqB;CACrB,mBAAmB;CACnB,qDAAqD;CACrD,kFAAkF;CAClF,kBAAkB;CAClB,+CAA+C;CAC/C,qBAAqB;CACrB,qBAAqB;CACrB,mBAAmB;CAEnB,gCAAgC;CAChC,kBAAkB;CAClB,iGAAiG;CACjG,4BAA4B;CAC5B,oBAAoB;CACpB,yCAAyC;CACzC,qBAAqB;CACrB,kGAAkG;CAClG,yBAAyB;CACzB,0BAA0B;CAC1B,sBAAsB;CACtB,qBAAqB;CACrB,sGAAsG;CACtG,yBAAyB;CACzB,0BAA0B;CAC1B,sBAAsB;CACtB,qBAAqB;CACrB,uGAAuG;CACvG,0BAA0B;CAC1B,0BAA0B;CAC1B,sBAAsB;CACtB,qBAAqB;CACrB,yGAAyG;CACzG,4BAA4B;CAC5B,0BAA0B;CAC1B,sBAAsB;CACtB,oBAAoB;CACpB,mBAAmB;CAEnB,0BAA0B;CAC1B,kBAAkB;CAClB,iGAAiG;CACjG,sBAAsB;CACtB,oBAAoB;CACpB,yCAAyC;CACzC,qBAAqB;CACrB,yGAAyG;CACzG,mCAAmC;CACnC,0BAA0B;CAC1B,sBAAsB;CACtB,qBAAqB;CACrB,uGAAuG;CACvG,qCAAqC;CACrC,0BAA0B;CAC1B,sBAAsB;CACtB,oBAAoB;CACpB,mBAAmB;CACnB,iBAAiB;CAEjB,6BAA6B;CAC7B,+DAA+D;CAC/D,qFAAqF;CACrF,oDAAoD;CACpD,iEAAiE;CACjE,mBAAmB;CACnB,iEAAiE;CACjE,2DAA2D;CAC3D,mBAAmB;CACnB,mBAAmB;CACnB,iBAAiB;CACjB,eAAe;CACf,gBAAgB;CAChB,OAAO;CACP,KAAK;CAEL,yBAAyB", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className={`flex-1 ${className}`}>\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAOA,MAAM,SAAgC;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IACjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAW,AAAC,UAAmB,OAAV;0BACxB;;;;;;0BAEH,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KAVM;uCAYS", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;KAZM;AAmBN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MANM;AAaN,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;MANM;AAaN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANM", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/job-listings.ts"], "sourcesContent": ["import { JobListing } from '@/types/blog';\n\nexport const jobListings: JobListing[] = [\n  {\n    id: \"1\",\n    title: \"Senior Frontend Developer\",\n    company: \"TechFlow Inc.\",\n    location: \"Remote (US)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/senior-frontend-developer\",\n    postedAt: \"2024-01-15\"\n  },\n  {\n    id: \"2\", \n    title: \"Product Manager\",\n    company: \"InnovateCorp\",\n    location: \"Remote (Global)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/product-manager\",\n    postedAt: \"2024-01-14\"\n  },\n  {\n    id: \"3\",\n    title: \"UX/UI Designer\",\n    company: \"DesignStudio\",\n    location: \"Remote (EU)\",\n    type: \"Contract\",\n    remote: true,\n    url: \"https://example.com/jobs/ux-ui-designer\",\n    postedAt: \"2024-01-13\"\n  },\n  {\n    id: \"4\",\n    title: \"<PERSON><PERSON>ps Engineer\",\n    company: \"CloudTech Solutions\",\n    location: \"Remote (Americas)\",\n    type: \"Full-time\",\n    remote: true,\n    url: \"https://example.com/jobs/devops-engineer\",\n    postedAt: \"2024-01-12\"\n  },\n  {\n    id: \"5\",\n    title: \"Content Marketing Specialist\",\n    company: \"GrowthHackers\",\n    location: \"Remote (Worldwide)\",\n    type: \"Part-time\",\n    remote: true,\n    url: \"https://example.com/jobs/content-marketing-specialist\",\n    postedAt: \"2024-01-11\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/blog/JobSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { ExternalLink, Briefcase, MapPin, Clock, Building } from 'lucide-react';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { jobListings } from '@/data/job-listings';\nimport { formatRelativeDate } from '@/lib/utils';\n\ninterface JobSidebarProps {\n  className?: string;\n}\n\nconst JobSidebar: React.FC<JobSidebarProps> = ({ className = '' }) => {\n  const latestJobs = jobListings.slice(0, 5);\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Latest Remote Jobs */}\n      <Card className=\"sticky top-24\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <div className=\"flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg\">\n              <Briefcase className=\"h-4 w-4 text-green-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">Latest Remote Jobs</h3>\n          </div>\n          <p className=\"text-sm text-gray-600\">\n            Fresh opportunities from top remote-first companies\n          </p>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {latestJobs.map((job) => (\n              <div key={job.id} className=\"group\">\n                <div className=\"border border-gray-100 rounded-lg p-3 hover:border-blue-200 hover:bg-blue-50/50 transition-all duration-200\">\n                  <h4 className=\"font-medium text-gray-900 text-sm mb-2 group-hover:text-blue-600 transition-colors\">\n                    {job.title}\n                  </h4>\n                  \n                  <div className=\"space-y-1 mb-3\">\n                    <div className=\"flex items-center gap-1 text-xs text-gray-600\">\n                      <Building className=\"h-3 w-3\" />\n                      <span>{job.company}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1 text-xs text-gray-600\">\n                      <MapPin className=\"h-3 w-3\" />\n                      <span>{job.location}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1 text-xs text-gray-600\">\n                      <Clock className=\"h-3 w-3\" />\n                      <span>{formatRelativeDate(job.postedAt)}</span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      job.type === 'Full-time' \n                        ? 'bg-green-100 text-green-800'\n                        : job.type === 'Part-time'\n                        ? 'bg-blue-100 text-blue-800'\n                        : job.type === 'Contract'\n                        ? 'bg-purple-100 text-purple-800'\n                        : 'bg-orange-100 text-orange-800'\n                    }`}>\n                      {job.type}\n                    </span>\n                    <a\n                      href={job.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-blue-600 hover:text-blue-700 text-xs font-medium flex items-center gap-1 transition-colors\"\n                    >\n                      Apply\n                      <ExternalLink className=\"h-3 w-3\" />\n                    </a>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"mt-4 pt-4 border-t border-gray-100\">\n            <Button variant=\"outline\" size=\"sm\" className=\"w-full\" asChild>\n              <a href=\"https://remoteok.io\" target=\"_blank\" rel=\"noopener noreferrer\">\n                View More Jobs\n                <ExternalLink className=\"ml-1 h-4 w-4\" />\n              </a>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Newsletter Signup */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-4\">\n              <Briefcase className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">Stay Updated</h3>\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Get weekly remote work tips and job alerts delivered to your inbox.\n            </p>\n            <div className=\"space-y-2\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"form-input text-sm\"\n              />\n              <Button size=\"sm\" className=\"w-full\">\n                Subscribe\n              </Button>\n            </div>\n            <p className=\"text-xs text-gray-500 mt-2\">\n              No spam, unsubscribe anytime\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Popular Categories */}\n      <Card>\n        <CardHeader>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Popular Topics</h3>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2\">\n            {[\n              'Remote Work Setup',\n              'Productivity Tips',\n              'Team Management',\n              'Work-Life Balance',\n              'Digital Nomad',\n              'Remote Tools'\n            ].map((topic) => (\n              <Link\n                key={topic}\n                href={`/blog?category=${encodeURIComponent(topic)}`}\n                className=\"block text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded transition-colors\"\n              >\n                {topic}\n              </Link>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default JobSidebar;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,aAAwC;QAAC,EAAE,YAAY,EAAE,EAAE;IAC/D,MAAM,aAAa,iIAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;IAExC,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC;wCAAiB,WAAU;kDAC1B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK;;;;;;8DAGZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAM,IAAI,OAAO;;;;;;;;;;;;sEAEpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;8EAAM,IAAI,QAAQ;;;;;;;;;;;;sEAErB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;8EAAM,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,QAAQ;;;;;;;;;;;;;;;;;;8DAI1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,AAAC,8CAQjB,OAPC,IAAI,IAAI,KAAK,cACT,gCACA,IAAI,IAAI,KAAK,cACb,8BACA,IAAI,IAAI,KAAK,aACb,kCACA;sEAEH,IAAI,IAAI;;;;;;sEAEX,6LAAC;4DACC,MAAM,IAAI,GAAG;4DACb,QAAO;4DACP,KAAI;4DACJ,WAAU;;gEACX;8EAEC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uCAxCtB,IAAI,EAAE;;;;;;;;;;0CAgDpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC5D,cAAA,6LAAC;wCAAE,MAAK;wCAAsB,QAAO;wCAAS,KAAI;;4CAAsB;0DAEtE,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;0CAIvC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;0BAQhD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAEtD,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;gCACA;gCACA;gCACA;gCACA;gCACA;6BACD,CAAC,GAAG,CAAC,CAAC,sBACL,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,AAAC,kBAA2C,OAA1B,mBAAmB;oCAC3C,WAAU;8CAET;mCAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;KAxIM;uCA0IS", "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/blog-posts.ts"], "sourcesContent": ["import { Blog<PERSON><PERSON>, Author } from '@/types/blog';\n\nconst author: Author = {\n  name: \"<PERSON>\",\n  bio: \"Remote work consultant and productivity expert with 8+ years of experience helping teams transition to distributed work environments.\",\n  avatar: \"/images/author-avatar.jpg\",\n  social: {\n    twitter: \"https://twitter.com/alex<PERSON><PERSON><PERSON>\",\n    linkedin: \"https://linkedin.com/in/alex<PERSON><PERSON><PERSON>\",\n    website: \"https://alexjohnson.dev\"\n  }\n};\n\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"The Ultimate Guide to Remote Work Productivity\",\n    slug: \"ultimate-guide-remote-work-productivity\",\n    excerpt: \"Discover proven strategies and tools to maximize your productivity while working from home. Learn how to create the perfect remote work environment.\",\n    content: `# The Ultimate Guide to Remote Work Productivity\n\nWorking remotely has become the new normal for millions of professionals worldwide. However, maintaining productivity while working from home presents unique challenges that require specific strategies and tools.\n\n## Creating Your Ideal Workspace\n\nYour physical environment plays a crucial role in your productivity. Here are key elements to consider:\n\n### 1. Dedicated Work Area\n- Choose a quiet space away from distractions\n- Ensure good lighting, preferably natural light\n- Invest in ergonomic furniture\n\n### 2. Essential Tools and Equipment\n- High-quality monitor and keyboard\n- Reliable internet connection\n- Noise-canceling headphones\n- Good webcam for video calls\n\n## Time Management Strategies\n\n### The Pomodoro Technique\nWork in focused 25-minute intervals followed by 5-minute breaks. This technique helps maintain concentration and prevents burnout.\n\n### Time Blocking\nSchedule specific time blocks for different types of work:\n- Deep work sessions\n- Meetings and calls\n- Email and communication\n- Administrative tasks\n\n## Communication Best Practices\n\nEffective communication is vital for remote teams:\n\n- **Over-communicate**: Share updates proactively\n- **Use the right tools**: Choose appropriate channels for different types of communication\n- **Set boundaries**: Establish clear working hours\n- **Regular check-ins**: Schedule consistent team meetings\n\n## Maintaining Work-Life Balance\n\nRemote work can blur the lines between personal and professional life. Here's how to maintain balance:\n\n1. **Set clear boundaries** between work and personal time\n2. **Create rituals** to start and end your workday\n3. **Take regular breaks** and step away from your workspace\n4. **Stay connected** with colleagues and friends\n\n## Conclusion\n\nRemote work productivity isn't just about having the right tools—it's about developing the right habits and mindset. By implementing these strategies consistently, you'll find yourself more productive and satisfied with your remote work experience.`,\n    featuredImage: \"/images/remote-productivity.jpg\",\n    category: \"Productivity\",\n    tags: [\"remote work\", \"productivity\", \"time management\", \"work from home\"],\n    author,\n    publishedAt: \"2024-01-15\",\n    readingTime: 8,\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Building a Strong Remote Team Culture\",\n    slug: \"building-strong-remote-team-culture\",\n    excerpt: \"Learn how to foster connection, collaboration, and culture in distributed teams. Essential strategies for remote team leaders.\",\n    content: `# Building a Strong Remote Team Culture\n\nCreating a cohesive team culture in a remote environment requires intentional effort and the right strategies. Here's how to build and maintain a strong remote team culture.\n\n## Foundation of Remote Culture\n\n### Clear Values and Mission\n- Define your team's core values\n- Communicate the mission regularly\n- Align individual goals with team objectives\n\n### Trust and Transparency\n- Foster open communication\n- Share information freely\n- Trust team members to manage their time\n\n## Virtual Team Building Activities\n\n### Regular Social Interactions\n- Virtual coffee chats\n- Online team games\n- Shared interest groups\n- Virtual lunch meetings\n\n### Collaborative Projects\n- Cross-functional initiatives\n- Mentorship programs\n- Knowledge sharing sessions\n\n## Communication Strategies\n\n### Structured Communication\n- Daily standups\n- Weekly team meetings\n- Monthly all-hands meetings\n- Quarterly reviews\n\n### Informal Communication\n- Slack channels for casual chat\n- Virtual water cooler conversations\n- Peer recognition programs\n\n## Recognition and Celebration\n\nCelebrating wins and recognizing contributions is crucial for remote teams:\n\n- Public recognition in team meetings\n- Peer-to-peer appreciation systems\n- Virtual celebration events\n- Achievement badges or rewards\n\n## Conclusion\n\nBuilding a strong remote team culture takes time and consistent effort, but the results are worth it. Teams with strong cultures are more engaged, productive, and resilient.`,\n    featuredImage: \"/images/team-culture.jpg\",\n    category: \"Team Management\",\n    tags: [\"team culture\", \"remote teams\", \"leadership\", \"collaboration\"],\n    author,\n    publishedAt: \"2024-01-10\",\n    readingTime: 6,\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Essential Tools for Remote Workers in 2024\",\n    slug: \"essential-tools-remote-workers-2024\",\n    excerpt: \"A comprehensive review of the best tools and software for remote work, from communication platforms to productivity apps.\",\n    content: `# Essential Tools for Remote Workers in 2024\n\nThe right tools can make or break your remote work experience. Here's a curated list of essential tools every remote worker should consider.\n\n## Communication Tools\n\n### Video Conferencing\n- **Zoom**: Industry standard for meetings\n- **Google Meet**: Integrated with Google Workspace\n- **Microsoft Teams**: Great for Microsoft ecosystem\n\n### Instant Messaging\n- **Slack**: Feature-rich team communication\n- **Discord**: Great for informal team chat\n- **Microsoft Teams**: All-in-one solution\n\n## Productivity Tools\n\n### Task Management\n- **Asana**: Project management and team collaboration\n- **Trello**: Visual project boards\n- **Notion**: All-in-one workspace\n\n### Time Tracking\n- **Toggl**: Simple time tracking\n- **RescueTime**: Automatic time tracking\n- **Clockify**: Free time tracking for teams\n\n## File Sharing and Storage\n\n### Cloud Storage\n- **Google Drive**: Integrated with Google Workspace\n- **Dropbox**: Reliable file sync\n- **OneDrive**: Microsoft ecosystem integration\n\n### Document Collaboration\n- **Google Docs**: Real-time collaboration\n- **Microsoft 365**: Comprehensive office suite\n- **Notion**: Wiki-style documentation\n\n## Security Tools\n\n### VPN Services\n- **NordVPN**: Reliable and fast\n- **ExpressVPN**: Great for streaming\n- **Surfshark**: Budget-friendly option\n\n### Password Management\n- **1Password**: User-friendly interface\n- **Bitwarden**: Open-source option\n- **LastPass**: Popular choice\n\n## Conclusion\n\nThe key is to choose tools that integrate well together and fit your team's workflow. Start with the basics and gradually add more specialized tools as needed.`,\n    featuredImage: \"/images/remote-tools.jpg\",\n    category: \"Tools & Software\",\n    tags: [\"tools\", \"software\", \"productivity\", \"remote work\"],\n    author,\n    publishedAt: \"2024-01-05\",\n    readingTime: 7,\n    featured: false\n  }\n];\n\nexport const categories = [\n  \"All\",\n  \"Productivity\",\n  \"Team Management\", \n  \"Tools & Software\",\n  \"Work-Life Balance\",\n  \"Career Development\"\n];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,SAAiB;IACrB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAoDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;YAAmB;SAAiB;QAC1E;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAsDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAc;SAAgB;QACrE;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAuDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAS;YAAY;YAAgB;SAAc;QAC1D;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/app/blog/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport Link from 'next/link';\nimport { Search, Clock, User, Calendar, Briefcase } from 'lucide-react';\nimport Layout from '@/components/layout/Layout';\nimport { <PERSON>, CardHeader, CardContent, CardFooter } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport JobSidebar from '@/components/blog/JobSidebar';\nimport { blogPosts, categories } from '@/data/blog-posts';\nimport { formatDate } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\n\nconst BlogPage: React.FC = () => {\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const filteredPosts = useMemo(() => {\n    return blogPosts.filter(post => {\n      const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;\n      const matchesSearch = searchQuery === '' || \n        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));\n      \n      return matchesCategory && matchesSearch;\n    });\n  }, [selectedCategory, searchQuery]);\n\n  return (\n    <Layout>\n      {/* Header */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-3xl mx-auto text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Remote Work Blog\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Discover insights, tips, and strategies to excel in your remote work journey.\n            </p>\n            \n            {/* Search Bar */}\n            <div className=\"relative max-w-md mx-auto\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search articles...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"form-input pl-10 w-full\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Category Filter */}\n      <section className=\"py-8 bg-white border-b\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex flex-wrap gap-2 justify-center\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                onClick={() => setSelectedCategory(category)}\n                className={cn(\n                  'px-4 py-2 rounded-full text-sm font-medium transition-colors',\n                  selectedCategory === category\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                )}\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n            {/* Main Content */}\n            <div className=\"lg:col-span-3\">\n              {filteredPosts.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4\">\n                <Search className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No articles found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Try adjusting your search or filter to find what you're looking for.\n              </p>\n              <Button variant=\"outline\" onClick={() => {\n                setSearchQuery('');\n                setSelectedCategory('All');\n              }}>\n                Clear Filters\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredPosts.map((post) => (\n                <Card key={post.id} className=\"h-full flex flex-col\">\n                  <CardHeader>\n                    {/* Featured Image Placeholder */}\n                    <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg mb-4 flex items-center justify-center\">\n                      <Briefcase className=\"h-12 w-12 text-blue-600\" />\n                    </div>\n                    \n                    {/* Category and Date */}\n                    <div className=\"flex items-center gap-2 text-sm text-gray-500 mb-3\">\n                      <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                        {post.category}\n                      </span>\n                      <span>•</span>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-3 w-3\" />\n                        <span>{formatDate(post.publishedAt)}</span>\n                      </div>\n                    </div>\n                    \n                    {/* Title */}\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-3 line-clamp-2\">\n                      <Link \n                        href={`/blog/${post.slug}`}\n                        className=\"hover:text-blue-600 transition-colors\"\n                      >\n                        {post.title}\n                      </Link>\n                    </h2>\n                  </CardHeader>\n                  \n                  <CardContent className=\"flex-1\">\n                    {/* Excerpt */}\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    \n                    {/* Tags */}\n                    <div className=\"flex flex-wrap gap-1 mb-4\">\n                      {post.tags.slice(0, 3).map((tag) => (\n                        <span\n                          key={tag}\n                          className=\"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs\"\n                        >\n                          #{tag}\n                        </span>\n                      ))}\n                    </div>\n                  </CardContent>\n                  \n                  <CardFooter>\n                    <div className=\"flex items-center justify-between w-full\">\n                      <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                        <div className=\"flex items-center gap-1\">\n                          <User className=\"h-4 w-4\" />\n                          <span>{post.author.name}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Clock className=\"h-4 w-4\" />\n                          <span>{post.readingTime} min</span>\n                        </div>\n                      </div>\n                      <Button variant=\"outline\" size=\"sm\" asChild>\n                        <Link href={`/blog/${post.slug}`}>\n                          Read More\n                        </Link>\n                      </Button>\n                    </div>\n                  </CardFooter>\n                </Card>\n              ))}\n            </div>\n          )}\n          \n              {/* Load More Button (for future pagination) */}\n              {filteredPosts.length > 0 && (\n                <div className=\"text-center mt-12\">\n                  <Button variant=\"outline\" size=\"lg\">\n                    Load More Articles\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            {/* Sidebar */}\n            <div className=\"lg:col-span-1\">\n              <JobSidebar />\n            </div>\n          </div>\n        </div>\n      </section>\n    </Layout>\n  );\n};\n\nexport default BlogPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAaA,MAAM,WAAqB;;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YAC5B,OAAO,+HAAA,CAAA,YAAS,CAAC,MAAM;mDAAC,CAAA;oBACtB,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;oBACxE,MAAM,gBAAgB,gBAAgB,MACpC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,KAAK,IAAI,CAAC,IAAI;2DAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;oBAE1E,OAAO,mBAAmB;gBAC5B;;QACF;0CAAG;QAAC;QAAkB;KAAY;IAElC,qBACE,6LAAC,yIAAA,CAAA,UAAM;;0BAEL,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAK1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,+HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACf,6LAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA,qBAAqB,WACjB,2BACA;0CAGL;+BATI;;;;;;;;;;;;;;;;;;;;0BAiBf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,MAAM,KAAK,kBAC5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC,qIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAU,SAAS;oDACjC,eAAe;oDACf,oBAAoB;gDACtB;0DAAG;;;;;;;;;;;iGAKL,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,mIAAA,CAAA,OAAI;gDAAe,WAAU;;kEAC5B,6LAAC,mIAAA,CAAA,aAAU;;0EAET,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAIvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,KAAK,QAAQ;;;;;;kFAEhB,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;0FAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;;;;;;;0EAKtC,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,AAAC,SAAkB,OAAV,KAAK,IAAI;oEACxB,WAAU;8EAET,KAAK,KAAK;;;;;;;;;;;;;;;;;kEAKjB,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EAErB,6LAAC;gEAAE,WAAU;0EACV,KAAK,OAAO;;;;;;0EAIf,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC;wEAEC,WAAU;;4EACX;4EACG;;uEAHG;;;;;;;;;;;;;;;;kEASb,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,6LAAC;8FAAM,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;sFAEzB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,6LAAC;;wFAAM,KAAK,WAAW;wFAAC;;;;;;;;;;;;;;;;;;;8EAG5B,6LAAC,qIAAA,CAAA,UAAM;oEAAC,SAAQ;oEAAU,MAAK;oEAAK,OAAO;8EACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAM,AAAC,SAAkB,OAAV,KAAK,IAAI;kFAAI;;;;;;;;;;;;;;;;;;;;;;;+CA9D/B,KAAK,EAAE;;;;;;;;;;oCA0EnB,cAAc,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;0CAQ1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAvLM;KAAA;uCAyLS", "debugId": null}}]}