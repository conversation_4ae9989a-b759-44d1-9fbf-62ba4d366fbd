{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatRelativeDate(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n  \n  if (diffInDays === 0) return 'Today';\n  if (diffInDays === 1) return 'Yesterday';\n  if (diffInDays < 7) return `${diffInDays} days ago`;\n  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;\n  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;\n  return `${Math.floor(diffInDays / 365)} years ago`;\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9 -]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\nexport function calculateReadingTime(content: string): number {\n  const wordsPerMinute = 200;\n  const words = content.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,UAAkB;IACnD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAErF,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,aAAa,GAAG,OAAO,AAAC,GAAa,OAAX,YAAW;IACzC,IAAI,aAAa,IAAI,OAAO,AAAC,GAA6B,OAA3B,KAAK,KAAK,CAAC,aAAa,IAAG;IAC1D,IAAI,aAAa,KAAK,OAAO,AAAC,GAA8B,OAA5B,KAAK,KAAK,CAAC,aAAa,KAAI;IAC5D,OAAO,AAAC,GAA+B,OAA7B,KAAK,KAAK,CAAC,aAAa,MAAK;AACzC;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC/D;AAEO,SAAS,qBAAqB,OAAe;IAClD,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', children, asChild = false, ...props }, ref) => {\n    const baseClasses = 'btn inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'btn-primary focus:ring-blue-500',\n      secondary: 'btn-secondary focus:ring-gray-500',\n      outline: 'btn-outline focus:ring-blue-500',\n      ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    };\n\n    const classes = cn(\n      baseClasses,\n      variants[variant],\n      sizes[size],\n      className\n    );\n\n    if (asChild) {\n      return (\n        <span className={classes}>\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAuF;QAAtF,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACnF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAK,WAAW;sBACd;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  LayoutDashboard, \n  FileText, \n  Plus, \n  Settings, \n  LogOut, \n  Menu, \n  X,\n  User,\n  BarChart3\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },\n  { name: 'All Posts', href: '/admin/posts', icon: FileText },\n  { name: 'New Post', href: '/admin/posts/new', icon: Plus },\n  { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },\n  { name: 'Settings', href: '/admin/settings', icon: Settings },\n];\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const pathname = usePathname();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0\",\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"\n      )}>\n        <div className=\"flex h-16 items-center justify-between px-6 border-b\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white\">\n              <FileText className=\"h-5 w-5\" />\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">Admin Panel</span>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden\"\n          >\n            <X className=\"h-6 w-6 text-gray-500\" />\n          </button>\n        </div>\n\n        <nav className=\"mt-6 px-3\">\n          <div className=\"space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n        </nav>\n\n        {/* User info at bottom */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t\">\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n              <User className=\"h-4 w-4 text-blue-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-900\">Alex Johnson</p>\n              <p className=\"text-xs text-gray-500\">Administrator</p>\n            </div>\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start text-gray-700\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            Sign Out\n          </Button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 bg-white shadow-sm border-b\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden\"\n            >\n              <Menu className=\"h-6 w-6 text-gray-500\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500\">\n                Welcome back, Alex!\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAjBA;;;;;;;AAuBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM,+NAAA,CAAA,kBAAe;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAgB,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC1D;QAAE,MAAM;QAAY,MAAM;QAAoB,MAAM,qMAAA,CAAA,OAAI;IAAC;IACzD;QAAE,MAAM;QAAa,MAAM;QAAoB,MAAM,qNAAA,CAAA,YAAS;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC7D;AAED,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gIACA,cAAc,kBAAkB;;kCAEhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;kCAQ9C,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAtGM;;QAEa,qIAAA,CAAA,cAAW;;;KAFxB;uCAwGS", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({ children, className, hover = true }) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-lg hover:-translate-y-1',\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mb-4', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('', className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardFooterProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQA,MAAM,OAA4B;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,SAAS,wCACT;kBAGD;;;;;;AAGP;KAZM;AAmBN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;kBACxB;;;;;;AAGP;MANM;AAaN,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACtE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;kBACpB;;;;;;AAGP;MANM;AAaN,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACtD;;;;;;AAGP;MANM", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/data/blog-posts.ts"], "sourcesContent": ["import { Blog<PERSON><PERSON>, Author } from '@/types/blog';\n\nconst author: Author = {\n  name: \"<PERSON>\",\n  bio: \"Remote work consultant and productivity expert with 8+ years of experience helping teams transition to distributed work environments.\",\n  avatar: \"/images/author-avatar.jpg\",\n  social: {\n    twitter: \"https://twitter.com/alex<PERSON><PERSON><PERSON>\",\n    linkedin: \"https://linkedin.com/in/alex<PERSON><PERSON><PERSON>\",\n    website: \"https://alexjohnson.dev\"\n  }\n};\n\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"The Ultimate Guide to Remote Work Productivity\",\n    slug: \"ultimate-guide-remote-work-productivity\",\n    excerpt: \"Discover proven strategies and tools to maximize your productivity while working from home. Learn how to create the perfect remote work environment.\",\n    content: `# The Ultimate Guide to Remote Work Productivity\n\nWorking remotely has become the new normal for millions of professionals worldwide. However, maintaining productivity while working from home presents unique challenges that require specific strategies and tools.\n\n## Creating Your Ideal Workspace\n\nYour physical environment plays a crucial role in your productivity. Here are key elements to consider:\n\n### 1. Dedicated Work Area\n- Choose a quiet space away from distractions\n- Ensure good lighting, preferably natural light\n- Invest in ergonomic furniture\n\n### 2. Essential Tools and Equipment\n- High-quality monitor and keyboard\n- Reliable internet connection\n- Noise-canceling headphones\n- Good webcam for video calls\n\n## Time Management Strategies\n\n### The Pomodoro Technique\nWork in focused 25-minute intervals followed by 5-minute breaks. This technique helps maintain concentration and prevents burnout.\n\n### Time Blocking\nSchedule specific time blocks for different types of work:\n- Deep work sessions\n- Meetings and calls\n- Email and communication\n- Administrative tasks\n\n## Communication Best Practices\n\nEffective communication is vital for remote teams:\n\n- **Over-communicate**: Share updates proactively\n- **Use the right tools**: Choose appropriate channels for different types of communication\n- **Set boundaries**: Establish clear working hours\n- **Regular check-ins**: Schedule consistent team meetings\n\n## Maintaining Work-Life Balance\n\nRemote work can blur the lines between personal and professional life. Here's how to maintain balance:\n\n1. **Set clear boundaries** between work and personal time\n2. **Create rituals** to start and end your workday\n3. **Take regular breaks** and step away from your workspace\n4. **Stay connected** with colleagues and friends\n\n## Conclusion\n\nRemote work productivity isn't just about having the right tools—it's about developing the right habits and mindset. By implementing these strategies consistently, you'll find yourself more productive and satisfied with your remote work experience.`,\n    featuredImage: \"/images/remote-productivity.jpg\",\n    category: \"Productivity\",\n    tags: [\"remote work\", \"productivity\", \"time management\", \"work from home\"],\n    author,\n    publishedAt: \"2024-01-15\",\n    readingTime: 8,\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Building a Strong Remote Team Culture\",\n    slug: \"building-strong-remote-team-culture\",\n    excerpt: \"Learn how to foster connection, collaboration, and culture in distributed teams. Essential strategies for remote team leaders.\",\n    content: `# Building a Strong Remote Team Culture\n\nCreating a cohesive team culture in a remote environment requires intentional effort and the right strategies. Here's how to build and maintain a strong remote team culture.\n\n## Foundation of Remote Culture\n\n### Clear Values and Mission\n- Define your team's core values\n- Communicate the mission regularly\n- Align individual goals with team objectives\n\n### Trust and Transparency\n- Foster open communication\n- Share information freely\n- Trust team members to manage their time\n\n## Virtual Team Building Activities\n\n### Regular Social Interactions\n- Virtual coffee chats\n- Online team games\n- Shared interest groups\n- Virtual lunch meetings\n\n### Collaborative Projects\n- Cross-functional initiatives\n- Mentorship programs\n- Knowledge sharing sessions\n\n## Communication Strategies\n\n### Structured Communication\n- Daily standups\n- Weekly team meetings\n- Monthly all-hands meetings\n- Quarterly reviews\n\n### Informal Communication\n- Slack channels for casual chat\n- Virtual water cooler conversations\n- Peer recognition programs\n\n## Recognition and Celebration\n\nCelebrating wins and recognizing contributions is crucial for remote teams:\n\n- Public recognition in team meetings\n- Peer-to-peer appreciation systems\n- Virtual celebration events\n- Achievement badges or rewards\n\n## Conclusion\n\nBuilding a strong remote team culture takes time and consistent effort, but the results are worth it. Teams with strong cultures are more engaged, productive, and resilient.`,\n    featuredImage: \"/images/team-culture.jpg\",\n    category: \"Team Management\",\n    tags: [\"team culture\", \"remote teams\", \"leadership\", \"collaboration\"],\n    author,\n    publishedAt: \"2024-01-10\",\n    readingTime: 6,\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Essential Tools for Remote Workers in 2024\",\n    slug: \"essential-tools-remote-workers-2024\",\n    excerpt: \"A comprehensive review of the best tools and software for remote work, from communication platforms to productivity apps.\",\n    content: `# Essential Tools for Remote Workers in 2024\n\nThe right tools can make or break your remote work experience. Here's a curated list of essential tools every remote worker should consider.\n\n## Communication Tools\n\n### Video Conferencing\n- **Zoom**: Industry standard for meetings\n- **Google Meet**: Integrated with Google Workspace\n- **Microsoft Teams**: Great for Microsoft ecosystem\n\n### Instant Messaging\n- **Slack**: Feature-rich team communication\n- **Discord**: Great for informal team chat\n- **Microsoft Teams**: All-in-one solution\n\n## Productivity Tools\n\n### Task Management\n- **Asana**: Project management and team collaboration\n- **Trello**: Visual project boards\n- **Notion**: All-in-one workspace\n\n### Time Tracking\n- **Toggl**: Simple time tracking\n- **RescueTime**: Automatic time tracking\n- **Clockify**: Free time tracking for teams\n\n## File Sharing and Storage\n\n### Cloud Storage\n- **Google Drive**: Integrated with Google Workspace\n- **Dropbox**: Reliable file sync\n- **OneDrive**: Microsoft ecosystem integration\n\n### Document Collaboration\n- **Google Docs**: Real-time collaboration\n- **Microsoft 365**: Comprehensive office suite\n- **Notion**: Wiki-style documentation\n\n## Security Tools\n\n### VPN Services\n- **NordVPN**: Reliable and fast\n- **ExpressVPN**: Great for streaming\n- **Surfshark**: Budget-friendly option\n\n### Password Management\n- **1Password**: User-friendly interface\n- **Bitwarden**: Open-source option\n- **LastPass**: Popular choice\n\n## Conclusion\n\nThe key is to choose tools that integrate well together and fit your team's workflow. Start with the basics and gradually add more specialized tools as needed.`,\n    featuredImage: \"/images/remote-tools.jpg\",\n    category: \"Tools & Software\",\n    tags: [\"tools\", \"software\", \"productivity\", \"remote work\"],\n    author,\n    publishedAt: \"2024-01-05\",\n    readingTime: 7,\n    featured: false\n  }\n];\n\nexport const categories = [\n  \"All\",\n  \"Productivity\",\n  \"Team Management\", \n  \"Tools & Software\",\n  \"Work-Life Balance\",\n  \"Career Development\"\n];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,SAAiB;IACrB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAoDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;YAAmB;SAAiB;QAC1E;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAsDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAgB;YAAgB;YAAc;SAAgB;QACrE;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAU;QAuDV,eAAe;QACf,UAAU;QACV,MAAM;YAAC;YAAS;YAAY;YAAgB;SAAc;QAC1D;QACA,aAAa;QACb,aAAa;QACb,UAAU;IACZ;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My-Blog-App/my-blog-app/src/app/admin/posts/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Plus, Edit, Trash2, Eye, Search, Filter, MoreHorizontal } from 'lucide-react';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { blogPosts, categories } from '@/data/blog-posts';\nimport { formatDate } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\n\nconst PostsManagement: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);\n\n  const filteredPosts = blogPosts.filter(post => {\n    const matchesSearch = searchQuery === '' || \n      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleSelectPost = (postId: string) => {\n    setSelectedPosts(prev => \n      prev.includes(postId) \n        ? prev.filter(id => id !== postId)\n        : [...prev, postId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    setSelectedPosts(\n      selectedPosts.length === filteredPosts.length \n        ? [] \n        : filteredPosts.map(post => post.id)\n    );\n  };\n\n  const handleDeleteSelected = () => {\n    if (confirm(`Are you sure you want to delete ${selectedPosts.length} post(s)?`)) {\n      // In a real app, this would make API calls to delete posts\n      console.log('Deleting posts:', selectedPosts);\n      setSelectedPosts([]);\n    }\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Posts</h1>\n            <p className=\"text-gray-600\">Manage your blog posts and articles</p>\n          </div>\n          <Button asChild>\n            <Link href=\"/admin/posts/new\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              New Post\n            </Link>\n          </Button>\n        </div>\n\n        {/* Filters and Search */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              {/* Search */}\n              <div className=\"relative flex-1\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search posts...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"form-input pl-10\"\n                />\n              </div>\n\n              {/* Category Filter */}\n              <div className=\"flex items-center gap-2\">\n                <Filter className=\"h-4 w-4 text-gray-500\" />\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"form-input min-w-[150px]\"\n                >\n                  {categories.map((category) => (\n                    <option key={category} value={category}>\n                      {category}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Bulk Actions */}\n            {selectedPosts.length > 0 && (\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between\">\n                <span className=\"text-sm text-blue-800\">\n                  {selectedPosts.length} post(s) selected\n                </span>\n                <div className=\"flex items-center gap-2\">\n                  <Button variant=\"outline\" size=\"sm\" onClick={handleDeleteSelected}>\n                    <Trash2 className=\"mr-1 h-4 w-4\" />\n                    Delete Selected\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Posts Table */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">\n                All Posts ({filteredPosts.length})\n              </h2>\n            </div>\n          </CardHeader>\n          <CardContent className=\"p-0\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gray-50 border-b\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedPosts.length === filteredPosts.length && filteredPosts.length > 0}\n                        onChange={handleSelectAll}\n                        className=\"rounded border-gray-300\"\n                      />\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Title\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Category\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Published\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {filteredPosts.map((post) => (\n                    <tr key={post.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedPosts.includes(post.id)}\n                          onChange={() => handleSelectPost(post.id)}\n                          className=\"rounded border-gray-300\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900 line-clamp-1\">\n                            {post.title}\n                          </div>\n                          <div className=\"text-sm text-gray-500 line-clamp-1\">\n                            {post.excerpt}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                          {post.category}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                          Published\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-500\">\n                        {formatDate(post.publishedAt)}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"flex items-center gap-2\">\n                          <Button variant=\"ghost\" size=\"sm\" asChild>\n                            <Link href={`/blog/${post.slug}`} target=\"_blank\">\n                              <Eye className=\"h-4 w-4\" />\n                            </Link>\n                          </Button>\n                          <Button variant=\"ghost\" size=\"sm\" asChild>\n                            <Link href={`/admin/posts/${post.id}`}>\n                              <Edit className=\"h-4 w-4\" />\n                            </Link>\n                          </Button>\n                          <Button \n                            variant=\"ghost\" \n                            size=\"sm\"\n                            onClick={() => {\n                              if (confirm('Are you sure you want to delete this post?')) {\n                                console.log('Deleting post:', post.id);\n                              }\n                            }}\n                          >\n                            <Trash2 className=\"h-4 w-4 text-red-500\" />\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {filteredPosts.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-500 mb-4\">\n                  <Search className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p>No posts found</p>\n                  <p className=\"text-sm\">Try adjusting your search or filter criteria</p>\n                </div>\n                <Button variant=\"outline\" onClick={() => {\n                  setSearchQuery('');\n                  setSelectedCategory('All');\n                }}>\n                  Clear Filters\n                </Button>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default PostsManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYA,MAAM,kBAA4B;;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,gBAAgB,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,gBAAgB,MACpC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAC7D,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,kBAAkB;QACtB,iBACE,cAAc,MAAM,KAAK,cAAc,MAAM,GACzC,EAAE,GACF,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;IAEzC;IAEA,MAAM,uBAAuB;QAC3B,IAAI,QAAQ,AAAC,mCAAuD,OAArB,cAAc,MAAM,EAAC,eAAa;YAC/E,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,iBAAiB,EAAE;QACrB;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAW;kBACV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,6LAAC,qIAAA,CAAA,UAAM;4BAAC,OAAO;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;0DAET,+HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAAsB,OAAO;kEAC3B;uDADU;;;;;;;;;;;;;;;;;;;;;;4BASpB,cAAc,MAAM,GAAG,mBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,cAAc,MAAM;4CAAC;;;;;;;kDAExB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;;8DAC3C,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU/C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAsC;wCACtC,cAAc,MAAM;wCAAC;;;;;;;;;;;;;;;;;sCAIvC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEACC,MAAK;gEACL,SAAS,cAAc,MAAM,KAAK,cAAc,MAAM,IAAI,cAAc,MAAM,GAAG;gEACjF,UAAU;gEACV,WAAU;;;;;;;;;;;sEAGd,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,6LAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAKnG,6LAAC;gDAAM,WAAU;0DACd,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEACC,MAAK;oEACL,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;oEACvC,UAAU,IAAM,iBAAiB,KAAK,EAAE;oEACxC,WAAU;;;;;;;;;;;0EAGd,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFACZ,KAAK,KAAK;;;;;;sFAEb,6LAAC;4EAAI,WAAU;sFACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;0EAInB,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ;;;;;;;;;;;0EAGlB,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAU;8EAAsG;;;;;;;;;;;0EAIxH,6LAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;;;;;;0EAE9B,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,UAAM;4EAAC,SAAQ;4EAAQ,MAAK;4EAAK,OAAO;sFACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gFAAC,MAAM,AAAC,SAAkB,OAAV,KAAK,IAAI;gFAAI,QAAO;0FACvC,cAAA,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGnB,6LAAC,qIAAA,CAAA,UAAM;4EAAC,SAAQ;4EAAQ,MAAK;4EAAK,OAAO;sFACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gFAAC,MAAM,AAAC,gBAAuB,OAAR,KAAK,EAAE;0FACjC,cAAA,6LAAC,8MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGpB,6LAAC,qIAAA,CAAA,UAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS;gFACP,IAAI,QAAQ,+CAA+C;oFACzD,QAAQ,GAAG,CAAC,kBAAkB,KAAK,EAAE;gFACvC;4EACF;sFAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uDArDjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;gCA+DvB,cAAc,MAAM,KAAK,mBACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;sDAEzB,6LAAC,qIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,SAAS;gDACjC,eAAe;gDACf,oBAAoB;4CACtB;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnB;GApOM;KAAA;uCAsOS", "debugId": null}}]}